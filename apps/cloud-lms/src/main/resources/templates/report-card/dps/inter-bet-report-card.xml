<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="18mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-1cm" border="6pt solid  #4CD394" padding="6mm" >
                <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0%" height="0%" >
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="278%" content-height="278%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="300%" height="300%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feColorMatrix type="matrix" values="0.5 0 0 0 0
                                                              0 2 0 0 0
                                                              0 0 0.1 0 0
                                                              0 0 0 0.3 0"/>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/BET BG.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="15mm" />
                    <fo:table-column column-width="118mm" />
                    <fo:table-column column-width="50mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding-top="-3mm" padding-left="-4mm">
                                <fo:block>
                                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/BET_logo.png")'
                                                         content-width="32mm" content-height="scale-to-fit"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="-1mm">
                                <fo:block margin-left="25mm" font-size="16pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="3pt" padding-top="-2mm">
                                    <fo:block  th:text="${#strings.toUpperCase(model.header.schoolName)}"/>
                                </fo:block>
                                <fo:block margin-left="35mm" font-size="13pt" font-weight="bold" font-family="Arial, sans-serif"  space-after="5pt">
                                    ENGLISH SKILLS REPORT CARD
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="-4mm">
                                <fo:block >
                                </fo:block>
                                <fo:block th:if="${model.header.orgSlug} == 'nal635825'">
                                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/NOIDA.png")'
                                                         content-width="50mm" content-height="scale-to-fit"  />
                                </fo:block>
                                <fo:block th:if="${model.header.orgSlug} != 'nal635825'">
                                    <fo:external-graphic th:src="@{${model.header.logo}}"
                                                         content-width="40mm" content-height="scale-to-fit"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="7pt" font-weight="bold" font-family="Arial, sans-serif" space-before="2mm" space-after="2mm">
                    NOTE : The Bharat English Test is a standardized assessment designed to evaluate English language proficiency across key areas such as reading, writing, listening, and speaking.
                    This test serves as a benchmark for language competency, identifying strengths and areas for improvement, and fostering both personal and professional development.
                </fo:block>

                <fo:table border="none">
                    <fo:table-column column-width="56mm" />
                    <fo:table-column column-width="53mm" />
                    <fo:table-column column-width="70mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell >
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" >
                                    <fo:inline-container border="1pt solid black" padding="1mm" width="9mm" margin-right="2mm" >
                                        <fo:block>Date</fo:block>
                                    </fo:inline-container>
                                    <fo:inline-container border="1pt solid black" padding="1mm" width="24mm" margin-right="2mm"  >
                                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.2mm" th:text="${model.header.date}"></fo:block>
                                    </fo:inline-container>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" >  </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border="0.3mm solid #d5e6e8" space-after="2mm" space-before="2mm"></fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-after="2mm">
                    Candidate Details
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="54mm" />
                    <fo:table-column column-width="32mm" />
                    <fo:table-column column-width="32mm" />
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="28mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell number-columns-spanned="4" padding-left="-8.7mm">
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" margin-bottom="4mm">
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="45mm"/>
                                        <fo:table-column column-width="104mm"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell >
                                                    <fo:block text-align="center">
                                                        Name of the Student
                                                    </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" >
                                                        <fo:block  th:text="${#strings.toUpperCase(model.header.studentName)}"/>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell number-rows-spanned="4" padding-left="1.5mm" padding-top="-2mm">
                                <fo:block th:attr="border=(${model.header.imageUrl} == null) ? null : '2mm solid #d5e6e8'"
                                          font-size="10pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                    <fo:instream-foreign-object content-width="70%" content-height="70%">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                            <defs>
                                                <filter id="brightnessFilter">
                                                    <feComponentTransfer>
                                                        <feFuncR type="linear" slope="1"/> <!-- Adjust the slope value to change brightness -->
                                                        <feFuncG type="linear" slope="1"/>
                                                        <feFuncB type="linear" slope="1"/>
                                                    </feComponentTransfer>
                                                </filter>
                                            </defs>
                                            <image  x="0" y="0" width="100%" height="100%"
                                                   th:xlink:href="@{${model.header.imageUrl}}"/>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding-left="-19.5mm">
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" margin-bottom="4mm" >
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="55.5mm"/>
                                        <fo:table-column column-width="16mm"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell >
                                                    <fo:block text-align="center" th:if="${model.header.programme != null }">
                                                        Programme
                                                    </fo:block>
                                                    <fo:block th:if="${model.header.programme == null }"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" th:if="${model.header.programme != null }">
                                                        <fo:block  th:text="${#strings.toUpperCase(model.header.programme)}"/>
                                                    </fo:block-container>
                                                    <fo:block-container  padding="0.5mm" height="2.5mm" th:if="${model.header.programme == null }">
                                                         <fo:block > </fo:block>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell number-columns-spanned="2">
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" >
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="26mm"/>
                                        <fo:table-column column-width="23mm"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell padding-left="6mm">
                                                    <fo:block text-align="center" th:if="${model.header.rollNo != null }">
                                                        Candidate ID
                                                    </fo:block>
                                                    <fo:block th:if="${model.header.rollNo == null }">&#160; </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" th:if="${model.header.rollNo != null }">
                                                        <fo:block  th:text="${#strings.toUpperCase(model.header.rollNo)}"/>
                                                    </fo:block-container>
                                                    <fo:block-container  padding="0.5mm" height="2.5mm" th:if="${model.header.rollNo == null }">
                                                        <fo:block > &#160;</fo:block>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" >
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="16mm"/>
                                        <fo:table-column column-width="6mm"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell >
                                                    <fo:block text-align="center" th:if="${model.header.gender != null }">
                                                        Sex(M/F)
                                                    </fo:block>
                                                    <fo:block th:if="${model.header.gender == null }"> &#160;</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" th:if="${model.header.gender != null }">
                                                        <fo:block text-align="center" th:text="${#strings.toUpperCase(model.header.gender)}"/>
                                                    </fo:block-container>
                                                    <fo:block-container  padding="0.5mm" height="2.5mm" th:if="${model.header.gender == null }">
                                                        <fo:block> &#160;</fo:block>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" margin-bottom="4mm"
                                          th:attr="padding-top=${model.header.programme == null ? '-6mm' : '0mm'}">
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="36mm"/>
                                        <fo:table-column column-width="16mm"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell >
                                                    <fo:block >
                                                        Nationality
                                                    </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" >
                                                        <fo:block >INDIAN</fo:block>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell number-columns-spanned="3" >
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" margin-bottom="2mm" >
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="21mm"/>
                                        <fo:table-column column-width="20mm"/>
                                        <fo:table-column column-width="3mm"/>
                                        <fo:table-column column-width="26mm"/>
                                        <fo:table-column column-width="16.5mm"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell >
                                                    <fo:block text-align="center" th:if="${model.header.dob != null }">
                                                        DOB
                                                    </fo:block>
                                                    <fo:block th:if="${model.header.dob == null }">&#160; </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" th:if="${model.header.dob != null }">
                                                        <fo:block  th:text="${#strings.toUpperCase(model.header.dob)}"/>
                                                    </fo:block-container>
                                                    <fo:block-container  padding="0.5mm" height="2.5mm" th:if="${model.header.dob == null }">
                                                        <fo:block>&#160; </fo:block>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell >
                                                    <fo:block text-align="center" th:if="${model.header.motherTongue != null }">
                                                        Mother Tongue
                                                    </fo:block>
                                                    <fo:block th:if="${model.header.motherTongue == null }">&#160; </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="-1mm">
                                                    <fo:block-container border="1pt solid black" padding="0.5mm" height="2.5mm" th:if="${model.header.motherTongue != null }">
                                                        <fo:block  th:text="${#strings.toUpperCase(model.header.motherTongue)}"/>
                                                    </fo:block-container>
                                                    <fo:block-container  padding="0.5mm" height="2.5mm" th:if="${model.header.motherTongue == null }">
                                                        <fo:block> &#160;</fo:block>
                                                    </fo:block-container>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell number-columns-spanned="4" >
                                <fo:block border-bottom="0.4mm solid #d5e6e8" space-before="2mm"  space-after="2mm" padding-top="1mm"
                                          th:attr="padding-right=( ${model.header.imageUrl} == null) ? '30mm' : '-3mm' "></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell number-columns-spanned="2" >
                                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="2mm" padding-top="3mm">
                                    National Curriculum Framework (NCF) Test - English
                                </fo:block>
                                <fo:block font-size="7pt"  font-family="Arial, sans-serif" space-before="1mm">
                                    NCF Test-English evaluates students' English proficiency in line with
                                    national standards, assessing skills in reading, writing, grammar,
                                    and communication to support educational goals.
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell number-columns-spanned="3" >
                                <fo:block font-size="9pt" font-weight="bold" font-family="Arial, sans-serif" background-color="#4CD394" space-before="2mm"  padding="1mm" text-align="center" border="0.1pt solid solid" padding-left="-9mm" padding-right="-2mm" margin-top="3mm" >
                                    Result
                                </fo:block>
                                <fo:block border="0.1pt solid black" padding-left="-9mm" padding-right="-2mm" border-bottom="none" background-color="#d5e6e8">
                                    <fo:block-container font-size="9pt" font-family="Arial, sans-serif" text-align="center" display-align="center">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="50%"/>
                                            <fo:table-column column-width="50%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell>
                                                        <fo:block text-align="center"  border-right="0.1pt solid black" padding="1mm" padding-left="5mm">Score</fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell>
                                                        <fo:block text-align="center" padding="1mm">Proficiency</fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block-container>
                                </fo:block>
                                <fo:block-container font-size="9pt" font-weight="bold" background-color="#d5e6e8" font-family="Arial, sans-serif" text-align="center" display-align="center" border="0.1pt solid black" padding-left="-9mm" padding-right="-2mm" border-top="none">
                                    <fo:table table-layout="fixed" width="100%">
                                        <fo:table-column column-width="50%"/>
                                        <fo:table-column column-width="50%"/>
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell>
                                                    <fo:block text-align="center" border-right="0.1pt solid black" padding="1mm" th:text="${model.body.scoreValue}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block text-align="center" padding="1mm"  th:text="${model.body.proficiencyValue}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block-container>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-bottom="0.4mm solid #d5e6e8" space-before="3mm"  space-after="3mm" ></fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="2mm" >
                    Received a score of 8, indicating that the proficiency is at Skilled level.
                </fo:block>
                <fo:block font-size="10pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-before="2mm">
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm" >
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">0</fo:block>
                        <fo:block font-weight="medium"  padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Not Attempted</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">1</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Beginner</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">2</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Elementary</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm" >
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">3</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Basic</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="17mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFA300">4</fo:block>
                        <fo:block  font-weight="medium"  padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Low-Intermediate</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFA300">5</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Intermediate</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="19mm" margin-right="0.5mm" >
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">6</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Upper-Intermediate</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">7</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Advanced</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">8</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Proficient</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">9</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Expert</fo:block>
                    </fo:inline-container>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="2mm" >
                    Score Details
                </fo:block>
                <fo:block font-size="7pt"  font-family="Arial, sans-serif" space-before="1mm" >
                    Candidates receive a score ranging from 1 to 9, along with a proficiency level scaling from beginner to expert. The score serves as a numerical representation
                    of the candidate's proficiency.
                </fo:block>
                <fo:block border-bottom="0.4mm solid #d5e6e8" space-before="3mm"  space-after="3mm" ></fo:block>

                <fo:table space-after="10mm" border="none" th:if="${model.body.sectionListSize == '1'}">
                    <fo:table-column column-width="2.2mm" />
                    <fo:table-column column-width="170.5mm" />
                    <fo:table-body font-size="8pt">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section1Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline th:text="${model.body.section1Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section1Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" border-top="none" padding-left="2mm" margin-right="2mm"  >
                                    <fo:inline font-weight="bold">Listening: </fo:inline>
                                    <fo:block>
                                        Measures the ability to comprehend spoken
                                        English by presenting different accents,
                                        contexts, and formats. Candidates are tested
                                        on their understanding of key information,
                                        ideas, and implied meanings.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table border="none" th:if="${model.body.sectionListSize == '2'}">
                    <fo:table-column column-width="2.2mm" />
                    <fo:table-column column-width="84mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="84mm" />
                    <fo:table-body font-size="8pt">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section1Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline th:text="${model.body.section1Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section1Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section2Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline th:text="${model.body.section2Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section2Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" border-top="none" padding-left="2mm" margin-right="2mm"  >
                                    <fo:inline font-weight="bold">Listening: </fo:inline>
                                    <fo:block>
                                        Measures the ability to comprehend spoken
                                        English by presenting different accents,
                                        contexts, and formats. Candidates are tested
                                        on their understanding of key information,
                                        ideas, and implied meanings.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" border-top="none" >
                                    <fo:inline font-weight="bold">Speaking: </fo:inline>
                                    <fo:block>
                                        Assesses fluency, coherence, pronunciation,
                                        and the ability to express ideas clearly.
                                        Candidates engage in structured dialogues or
                                        respond to prompts to demonstrate their
                                        spoken communication skills.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table border="none" th:if="${model.body.sectionListSize == '3'}">
                    <fo:table-column column-width="2.2mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-body font-size="8pt">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section1Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline th:text="${model.body.section1Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section1Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section2Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline th:text="${model.body.section2Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section2Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section3Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline th:text="${model.body.section3Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section3Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" border-top="none" padding-left="2mm" margin-right="2mm"  >
                                    <fo:inline font-weight="bold">Listening: </fo:inline>
                                    <fo:block>
                                        Measures the ability to comprehend spoken
                                        English by presenting different accents,
                                        contexts, and formats. Candidates are tested
                                        on their understanding of key information,
                                        ideas, and implied meanings.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" border-top="none" >
                                    <fo:inline font-weight="bold">Speaking: </fo:inline>
                                    <fo:block>
                                        Assesses fluency, coherence, pronunciation,
                                        and the ability to express ideas clearly.
                                        Candidates engage in structured dialogues or
                                        respond to prompts to demonstrate their
                                        spoken communication skills.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" border-top="none" >
                                    <fo:inline font-weight="bold">Reading: </fo:inline>
                                    <fo:block padding-bottom="3.4mm">
                                        Evaluates comprehension through passages
                                        of varying complexity. It tests the ability to
                                        interpret meaning, analyze structure,
                                        understand vocabulary, and identify key
                                        themes or details.
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table border="none" th:if="${model.body.sectionListSize == '4'}">
                    <fo:table-column column-width="2.2mm" />
                    <fo:table-column column-width="41mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="41mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="41mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="42mm" />
                    <fo:table-body font-size="8pt">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline  th:text="${model.body.section1Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline font-size="7pt" th:text="${model.body.section1Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section1Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section2Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline font-size="7pt" th:text="${model.body.section2Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section2Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section3Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline font-size="7pt" th:text="${model.body.section3Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section3Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" background-color="#4CD394" padding-top="1mm">
                                    <fo:inline th:text="${model.body.section4Name}"></fo:inline>
                                    <fo:block line-height="1.5">
                                        <fo:table table-layout="fixed" width="100%">
                                            <fo:table-column column-width="98%"/>
                                            <fo:table-column column-width="2%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell height="4mm">
                                                        <fo:block line-height="1.5">
                                                            <fo:inline font-size="7pt" th:text="${model.body.section4Grade}"></fo:inline>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding-top="0.5mm">
                                                        <fo:block padding-left="5mm" margin-top="-5mm" text-align="center">
                                                            <fo:instream-foreign-object width="20pt" height="10pt">
                                                                <svg width="40%" height="40%" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="25%" cy="25%" r="25%" fill="white"/>
                                                                    <text x="25%" y="37%" dominant-baseline="middle" text-anchor="middle" fill="green" font-size="12pt" font-weight="bold" th:text="${model.body.section4Value}"></text>
                                                                </svg>
                                                            </fo:instream-foreign-object>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" border-top="none" padding-left="2mm" margin-right="2mm"  >
                                    <fo:inline font-weight="bold">Listening: </fo:inline>
                                    <fo:block>
                                        Measures the ability to comprehend spoken
                                        English by presenting different accents,
                                        contexts, and formats. Candidates are tested
                                        on their understanding of key information,
                                        ideas, and implied meanings.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" border-top="none" >
                                    <fo:inline font-weight="bold">Speaking: </fo:inline>
                                    <fo:block>
                                        Assesses fluency, coherence, pronunciation,
                                        and the ability to express ideas clearly.
                                        Candidates engage in structured dialogues or
                                        respond to prompts to demonstrate their
                                        spoken communication skills.</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" border-top="none" >
                                    <fo:inline font-weight="bold">Reading: </fo:inline>
                                    <fo:block padding-bottom="3.4mm">
                                        Evaluates comprehension through passages
                                        of varying complexity. It tests the ability to
                                        interpret meaning, analyze structure,
                                        understand vocabulary, and identify key
                                        themes or details.
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" margin-right="2mm" border-top="none" >
                                    <fo:inline font-weight="bold">Writing: </fo:inline>
                                    <fo:block >
                                        Measures the candidate’s ability to
                                        organize thoughts, use
                                        appropriate language, and convey
                                        ideas effectively. Tasks may
                                        include essay writing or structured
                                        responses, focusing on grammar,
                                        coherence, and clarity.
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block border-bottom="0.4mm solid #d5e6e8" space-before="3mm"  space-after="3mm" ></fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif"  space-after="3mm">
                    Speech Analysis
                </fo:block>

                <fo:table border="none">
                    <fo:table-column column-width="2.2mm" />
                    <fo:table-column column-width="60mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="25mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="25mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="25mm" />
                    <fo:table-column column-width="2.5mm" />
                    <fo:table-column column-width="25mm" />
                    <fo:table-body font-size="8pt" th:each="speech : ${model.body.speechAnalysis}">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block border="1pt solid #4CD394" padding-left="2mm" >
                                    <fo:block  margin-bottom="2mm"/>
                                    <fo:inline font-weight="bold" margin-top="5mm">Pronunciation Score</fo:inline>
                                    <fo:block>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="160" height="49.6">
                                                <circle cx="25" cy="25" r="20" stroke="#e0e0e0" stroke-width="4" fill="none"/>
                                                <circle cx="25" cy="25" r="20" stroke="#4CD394" stroke-width="4" fill="none"
                                                        stroke-dasharray="126" th:attr="stroke-dashoffset=${speech.pronunciationScore}"
                                                        transform="rotate(-90 25 25)"/>

                                                <text x="25" y="30" font-size="12" text-anchor="middle" fill="#000000" th:text="${speech.fluencyScore}"></text>
                                                <text x="60" y="20" font-size="8" >Accuracy Score</text>
                                                <text x="135" y="20" font-size="8" th:text="${speech.fluencyScore} + '/100'"></text>
                                                <line x1="60" y1="25" x2="160" y2="25" stroke="#e0e0e0" stroke-width="4"/>
                                                <g transform="rotate(0, 100, 25)">
                                                    <line x1="60" y1="25" y2="25" stroke="#4CD394" stroke-width="4"
                                                          th:attr="x2=${speech.accuracyScore}"/>
                                                </g>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block border="1pt solid #4CD394" text-align="center" background-color="#abf7b1" >
                                    <fo:external-graphic src="url('https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/toeic.svg')" content-width="147%" content-height="147%" padding-bottom="1mm"  />
                                    <fo:block text-align="center" font-size="10" th:text="${speech.toeicScore}"></fo:block>
                                    <fo:block text-align="center" font-size="10" padding-bottom="0.5mm">TOEIC</fo:block>
                                </fo:block>
                            </fo:table-cell>score
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block border="1pt solid #4CD394" text-align="center" background-color="#abf7b1">
                                    <fo:external-graphic src="url('https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/cefr.svg')" content-width="170%" content-height="170%" padding-bottom="1mm" />
                                    <fo:block text-align="center" font-size="10" th:attr="padding-bottom=${speech.cefrScore == null ? '4.5mm' : '0mm'}"
                                              th:text="${speech.cefrScore != null ? speech.cefrScore : ''}"> </fo:block>
                                    <fo:block text-align="center" font-size="10" padding-bottom="0.5mm">CEFR</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block border="1pt solid #4CD394" text-align="center" background-color="#abf7b1">
                                    <fo:external-graphic src="url('https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/bet.svg')" content-width="170%" content-height="170%" padding-bottom="1mm" />
                                    <fo:block text-align="center" font-size="10" th:text="${speech.ieltsScore}" ></fo:block>
                                    <fo:block text-align="center" font-size="10" padding-bottom="0.5mm">IELTS</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block border="1pt solid #4CD394" text-align="center" background-color="#abf7b1">
                                    <fo:external-graphic src="url('https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/pet.svg')" content-width="170%" content-height="170%" padding-bottom="1mm" />
                                    <fo:block  text-align="center" font-size="10" th:text="${speech.pteScore}"></fo:block>
                                    <fo:block text-align="center" font-size="10" padding-bottom="0.5mm">PTE</fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block border-bottom="0.4mm solid #d5e6e8" space-before="3mm"  space-after="3mm" ></fo:block>


                <fo:table border="none">
                    <fo:table-column column-width="60mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="65mm" />
                    <fo:table-body font-size="8pt">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block-container  padding-left="2mm" margin-right="2mm" height="25mm">
                                    <fo:block >
                                        <fo:instream-foreign-object content-width="80%" content-height="80%">
                                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                                <image  x="0" y="0" width="100%" height="100%"
                                                        th:xlink:href="@{${model.header.qrCodeUrl}}"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:block-container>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block-container  padding-left="2mm" margin-right="2mm" height="25mm">
                                    <!--<fo:block text-align="left" font-size="12pt" padding-top="10mm" font-weight="bold" >
                                        CERTIFIED BY
                                    </fo:block>-->
                                    <fo:block> </fo:block>
                                    <fo:block  text-align="right" padding-bottom="2mm" padding-top="-10mm" padding-left="2mm" th:if="${model.header.orgSlug} == 'nal635825'">
                                        <fo:external-graphic src="url('https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/NOIDA.png')" content-width="10%" content-height="5%" />
                                    </fo:block>
                                </fo:block-container>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block text-align="right" padding-bottom="2mm"  th:if="${model.body.proctoringStatus == null}">
                                    <fo:block> </fo:block>
                                </fo:block>
                                <fo:block text-align="right" padding-bottom="2mm"  th:if="${model.body.proctoringStatus == 'HONEST'}">
                                    <fo:external-graphic src="url(https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/CERTIFIED.png)"
                                                         content-width="27mm" content-height="27mm"/>
                                </fo:block>
                                <fo:block text-align="right" padding-bottom="2mm"  th:if="${model.body.proctoringStatus == 'SUSPICIOUS'}">
                                    <fo:external-graphic src="url(https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/SUSPICIOUS.png)"
                                                         content-width="27mm" content-height="27mm"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table>
                    <fo:table-column column-width="28mm" />
                    <fo:table-body>
                     <fo:table-row height="8mm">
                         <fo:table-cell background-color="white">
                             <fo:block space-after="-1.5mm" color="blue" space-before="2mm">
                                 <fo:inline>
                                 </fo:inline>
                                 <fo:inline text-align="left" font-size="6" padding-left="6mm">
                                     Scan to Verify
                                 </fo:inline>
                             </fo:block>
                             <fo:block color="blue">
                                 <fo:inline>
                                 </fo:inline>
                                 <fo:inline text-align="left" font-size="6" padding-left="7mm">
                                     Authenticity
                                 </fo:inline>
                             </fo:block>
                         </fo:table-cell>
                     </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-1cm" border="6pt solid  #4CD394" padding="6mm" >
                <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0%" height="0%" >
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="278%" content-height="278%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="300%" height="300%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feColorMatrix type="matrix" values="0.5 0 0 0 0
                                                              0 2 0 0 0
                                                              0 0 0.1 0 0
                                                              0 0 0 0.3 0"/>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/BET BG.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="20mm" />
                    <fo:table-column column-width="140mm" />
                    <fo:table-column column-width="50mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding-top="-3mm" padding-left="-4mm">
                                <fo:block >
                                    <fo:external-graphic src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/BET_logo.png")'
                                                         content-width="32mm" content-height="scale-to-fit"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="-4mm">
                                <fo:block font-size="13pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="5pt" padding-top="5mm">
                                    ENGLISH SKILLS PROFICIENCY SCALE
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="-4mm">
                                <fo:block> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="7pt" font-weight="bold" font-family="Arial, sans-serif" space-before="10mm" space-after="2mm">
                    NOTE : Bharat English Test establishes a standard for evaluating student's proficiency in the English language.
                </fo:block>
                <fo:block border-bottom="0.4mm solid #d5e6e8" space-before="5mm"  space-after="6mm" ></fo:block>
                <fo:block font-size="10pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-before="2mm">
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm" >
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">0</fo:block>
                        <fo:block font-weight="medium"  padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Not Attempted</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">1</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Beginner</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">2</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Elementary</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm" >
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFCC73">3</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Basic</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="17mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFA300">4</fo:block>
                        <fo:block  font-weight="medium"  padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Low-Intermediate</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#FFA300">5</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Intermediate</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="19mm" margin-right="0.5mm" >
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">6</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Upper-Intermediate</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">7</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Proficient</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">8</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Advanced</fo:block>
                    </fo:inline-container>
                    <fo:inline-container border="1pt solid black" padding="1mm" width="16mm" margin-right="0.5mm">
                        <fo:block  border="1pt solid black" padding="0.5mm" padding-bottom="-0.5mm" background-color="#4CD394">9</fo:block>
                        <fo:block font-weight="medium"   padding="0.5mm" padding-bottom="-0.5mm" font-size="5pt">Expert</fo:block>
                    </fo:inline-container>
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 9: Expert
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user has near-native fluency in English. They can handle highly complex and nuanced language effortlessly in both formal and informal
                    contexts, with complete accuracy and appropriateness.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 8: Advanced
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user has an excellent command of English with only rare errors. They can understand and express complex ideas clearly and precisely,
                    even in demanding situations, such as debates or negotiations.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 7: Proficient
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user communicates effectively in almost all situations. They can handle complex discussions, detailed reasoning, and a variety of
                    topics, though occasional errors or misunderstandings may occur.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 6: Upper-Intermediate
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user has a strong command of English. They can discuss familiar topics in detail and handle more complex or unfamiliar subjects with
                    some occasional mistakes or gaps in understanding.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 5: Intermediate
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user has a good working knowledge of English. They can manage routine communication and engage in discussions on familiar topics,
                    though errors and misunderstandings occur more frequently with complex language.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 4: Low-Intermediate
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user can communicate in basic conversations and perform simple tasks in English. Their understanding and use of more complex
                    language are limited, and they may struggle in less familiar situations.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 3: Basic
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user can understand and express very basic ideas in familiar contexts. Their ability to use English is limited to simple interactions and
                    short exchanges, with frequent errors.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 2: Elementary
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user has a minimal grasp of English. They can only understand and use very basic phrases and sentences, mostly in familiar contexts,
                    with great difficulty in more advanced conversations.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" space-before="3mm" space-after="1mm">
                    Level 1: Beginner
                </fo:block>
                <fo:block font-size="8pt"  font-family="Arial, sans-serif"  space-after="3mm">
                    The user has extremely limited or no ability to use English. They may know a few words or phrases but cannot engage in meaningful
                    communication.
                </fo:block>
                <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" padding-top="55mm">
                    <fo:inline> </fo:inline>
                    <fo:inline padding-left="140mm">
                        <fo:external-graphic src="url('https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/WeXL_logo.png')"
                                             content-width="80%" content-height="80%"/>
                    </fo:inline>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>