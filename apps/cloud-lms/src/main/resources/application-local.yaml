spring:
#  main:
#    lazy-initialization: true
  datasource :
    url: ***************************************************************
    username: bet_retail_user
    password: UBjdfhg8735
    hikari:
      maximum-pool-size: 2
  liquibase:
    enabled: false
  jpa:
    hibernate:
      ddl-auto: none
server:
  port: 8070
  servlet:
    context-path: /api
app:
  orgs:
    deletion:
      enabled: false
  publicDomainUrl: http://localhost:8070/
  betDomainUrl:  https://bet.academyteacher.com/
  localeFilesLocation: file:///home/<USER>/repos/wexl-apps-i18n/i18n/
  wordFinderUrl: https://api.dictionaryapi.dev/api/v2/entries/en/
  shinkan:
    enabled: true
    url: http://api.qria.co.in/api/Applicant/GetReportStatus
    apiKey: 1d7b229b-8143-4dd3-85ae-9ffcc2f7ce0e
    mail: <EMAIL>
  sns:
    topicArn: arn:aws:sns:ap-south-1:473077116069:wexl-retail-notifications-nonprod
  batch:
    url: https://learn.academyteacher.com/api/batch/job
  storageBucket: wexl-student-info-wasabi-nonprod
  storage: wexledu-wasabi-nonprod
  parseable:
    streamName: NR
    enabled: false

logging:
  level:
    com:
      wexl: DEBUG
    org:
      springframework:
        boot:
          autoconfigure: ERROR
        web: ERROR
        servlet:
          handler: ERROR
    org.apache.fop: OFF
urls:
  content: https://bet.academyteacher.com/api/content/
  correction: https://learn.academyteacher.com/api/correction/
  dpsIntegrationService: http://localhost:8080
langchain4j:
  open-ai:
    chat-model:
      api-key: ********************************************************************************************************************************************************************
      model-name: gpt-4o
      temperature: 0.7
      timeout: PT3M
      log-requests: false
      log-responses: false
      response-format: json_schema
