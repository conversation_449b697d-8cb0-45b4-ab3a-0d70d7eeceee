---
openapi: 3.0.0
info:
  title: Outline API
  description: |
    # Introduction

    The Outline API is structured in an RPC style. It enables you to
    programatically interact with all aspects of Outline’s data – in fact, the
    main application is built on exactly the same API.

    The API structure is available as an
    [openapi specification](https://github.com/outline/openapi) if that’s your
    jam – it can be used to generate clients for most programming languages.

    # Making requests

    Outline’s API follows simple RPC style conventions where each API endpoint is
    a `POST` method on `https://app.getoutline.com/api/:method`. Only HTTPS is
    supported and all response payloads are JSON.

    When making `POST` requests, request parameters are parsed depending on
    Content-Type header. To make a call using JSON payload, you must pass
    Content-Type: application/json header, here’s an example using CURL:

    ```
    curl https://app.getoutline.com/api/documents.info \
    -X 'POST' \
    -H 'authorization: Bearer MY_API_KEY' \
    -H 'content-type: application/json' \
    -H 'accept: application/json' \
    -d '{"id": "outline-api-NTpezNwhUP"}'
    ```

    Or, with JavaScript:

    ```javascript
    const response = await fetch("https://app.getoutline.com/api/documents.info", {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer MY_API_KEY"
      }
    })

    const body = await response.json();
    const document = body.data;
    ```

    # Authentication

    ## API key

    You can create new API keys under **Settings => API & Apps**. Be
    careful when handling your keys as they give access to all of your documents,
    you should treat them like passwords and they should never be committed to
    source control.

    To authenticate with API, you should supply the API key as the `Authorization` header
    (`Authorization: Bearer YOUR_API_KEY`).

    ## OAuth 2.0

    OAuth 2.0 is a widely used protocol for authorization and authentication. It allows users
    to grant third-party _or_ internal applications access to their resources without sharing
    their credentials. To use OAuth 2.0 you need to follow these steps:

    1. Register your application under **Settings => Applications**
    2. Obtain an access token by exchanging the client credentials for an access token
    3. Use the access token to authenticate requests to the API

    Some API endpoints allow unauthenticated requests for public resources and
    they can be called without authentication

    # Scopes

    Scopes are used to limit the access of an API key or application to specific resources. For example,
    an application may only need access to read documents, but not write them. Scopes can
    be global in the case of `read` and `write` scopes, specific to an API endpoint like
    `documents.read` and `documents.create`, or use wildcard scopes like `documents.*`.

    Some examples of scopes that can be used are:

    - `documents.read`: Allows reading documents
    - `documents.write`: Allows writing documents
    - `documents.*`: Allows all document-related actions
    - `users.*`: Allows all user-related actions
    - `read`: Allows all read actions
    - `write`: Allows all write actions

    # Errors

    All successful API requests will be returned with a 200 or 201 status code
    and `ok: true` in the response payload. If there’s an error while making the
    request, the appropriate status code is returned with the error message:

    ```
    {
      "ok": false,
      "error": "Not Found"
    }
    ```

    # Pagination

    Most top-level API resources have support for "list" API methods. For instance,
    you can list users, documents, and collections. These list methods share
    common parameters, taking both `limit` and `offset`.

    Responses will echo these parameters in the root `pagination` key, and also
    include a `nextPath` key which can be used as a handy shortcut to fetch the
    next page of results. For example:

    ```
    {
      ok: true,
      status: 200,
      data: […],
      pagination: {
        limit: 25,
        offset: 0,
        nextPath: "/api/documents.list?limit=25&offset=25"
      }
    }
    ```

    # Rate limits

    Like most APIs, Outline has rate limits in place to prevent abuse. Endpoints
    that mutate data are more restrictive than read-only endpoints. If you exceed
    the rate limit for a given endpoint, you will receive a `429 Too Many Requests`
    status code.

    The response will include a `Retry-After` header that indicates how many seconds
    you should wait before making another request.

    # Policies

    Most API resources have associated "policies", these objects describe the
    current API keys authorized actions related to an individual resource. It
    should be noted that the policy "id" is identical to the resource it is
    related to, policies themselves do not have unique identifiers.

    For most usecases of the API, policies can be safely ignored. Calling
    unauthorized methods will result in the appropriate response code – these can
    be used in an interface to adjust which elements are visible.
  version: 0.1.0
  contact:
    email: <EMAIL>
servers:
- url: https://app.getoutline.com/api
  description: Production
security:
- http: []
tags:
- name: Attachments
  description: |
    `Attachments` represent a file uploaded to cloud storage. They are created
    before the upload happens from the client and store all the meta information
    such as file type, size, and location.
- name: Auth
  description: |
    `Auth` represents the current API Keys authentication details. It can be
    used to check that a token is still valid and load the IDs for the current
    user and team.
- name: Collections
  description: |
    `Collections` represent grouping of documents in the knowledge base, they
    offer a way to structure information in a nested hierarchy and a level
    at which read and write permissions can be granted to individual users or
    groups of users.
- name: Comments
  description: |
    `Comments` represent a comment either on a selection of text in a document
    or on the document itself.
- name: Documents
  description: |
    `Documents` are what everything else revolves around. A document represents
    a single page of information and always returns the latest version of the
    content. Documents are stored in [Markdown](https://spec.commonmark.org/)
    formatting.
- name: Events
  description: |
    `Events` represent an artifact of an action. Whether it is creating a user,
    editing a document, changing permissions, or any other action – an event
    is created that can be used as an audit trail or activity stream.
- name: FileOperations
  description: |
    `FileOperations` represent background jobs for importing or exporting files.
    You can query the file operation to find the state of progress and any
    resulting output.
- name: Groups
  description: |
    `Groups` represent a list of users that logically belong together, for
    example there might be groups for each department in your organization.
    Groups can be granted access to collections with read or write permissions.
- name: Revisions
  description: |
    `Revisions` represent a snapshop of a document at a point in time. They
    are used to keep tracking of editing and collaboration history – a document
    can also be restored to a previous revision if neccessary.
- name: Shares
  description: |
    `Shares` represent authorization to view a document without being a member
    of the team. Shares are created in order to give access to documents publicly.
    Each user that shares a document will have a unique share object.
- name: Stars
  description: |
    `Stars` represent a favorited document or collection in the application sidebar.
    Each user has their own collection of starred items.
- name: Users
  description: |
    `Users` represent an individual with access to the knowledge base. Users
    can be created automatically when signing in with SSO or when a user is
    invited via email.
- name: Views
  description: |
    `Views` represent a compressed record of an individual users views of a
    document. Individual views are not recorded but a first, last and total
    is kept per user.
paths:
  "/attachments.create":
    post:
      tags:
      - Attachments
      summary: Create an attachment
      description: Creating an attachment object creates a database record and returns
        the inputs needed to generate a signed url and upload the file from the client
        to cloud storage.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: image.png
                documentId:
                  type: string
                  description: Identifier for the associated document, if any.
                  format: uuid
                contentType:
                  type: string
                  example: image/png
                size:
                  type: number
                  description: Size of the file attachment in bytes.
              required:
              - name
              - contentType
              - size
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      maxUploadSize:
                        type: number
                      uploadUrl:
                        type: string
                        format: uri
                      form:
                        type: object
                      attachment:
                        "$ref": "#/components/schemas/Attachment"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: attachmentsCreate
  "/attachments.redirect":
    post:
      tags:
      - Attachments
      summary: Retrieve an attachment
      description: Load an attachment from where it is stored based on the id. If
        the attachment is private then a temporary, signed url with embedded credentials
        is generated on demand.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the attachment.
                  format: uuid
              required:
              - id
      responses:
        '302':
          description: The url for the attachment
      operationId: attachmentsRedirect
  "/attachments.delete":
    post:
      tags:
      - Attachments
      summary: Delete an attachment
      description: Deleting an attachment is permanant. It will not delete references
        or links to the attachment that may exist in your documents.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                  description: Unique identifier for the attachment.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: attachmentsDelete
  "/auth.info":
    post:
      tags:
      - Auth
      summary: Retrieve auth
      description: Retrieve authentication details for the current API key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Auth"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: authInfo
  "/auth.config":
    post:
      tags:
      - Auth
      summary: Retrieve auth config
      description: Retrieve authentication options
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      name:
                        type: string
                        example: Acme Inc
                      hostname:
                        type: string
                        example: acme-inc.getoutline.com
                      services:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              example: slack
                            name:
                              type: string
                              example: Slack
                            authUrl:
                              type: string
                              example: https://acme-inc.getoutline.com/auth/slack
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: authConfig
  "/collections.info":
    post:
      tags:
      - Collections
      summary: Retrieve a collection
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the collection.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Collection"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsInfo
  "/collections.documents":
    post:
      tags:
      - Collections
      summary: Retrieve a collections document structure
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the collection.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/NavigationNode"
                    example: []
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsDocuments
  "/collections.list":
    post:
      tags:
      - Collections
      summary: List all collections
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  query:
                    type: string
                    description: If set, will filter the results by collection name.
                  statusFilter:
                    type: array
                    items:
                      "$ref": "#/components/schemas/CollectionStatus"
                    description: An optional array of statuses to filter by.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Collection"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsList
  "/collections.create":
    post:
      tags:
      - Collections
      summary: Create a collection
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: Human Resources
                description:
                  type: string
                  example: ''
                permission:
                  "$ref": "#/components/schemas/Permission"
                color:
                  type: string
                  example: "#123123"
                private:
                  type: boolean
                  example: false
              required:
              - name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Collection"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsCreate
  "/collections.update":
    post:
      tags:
      - Collections
      summary: Update a collection
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                  example: Human Resources
                permission:
                  "$ref": "#/components/schemas/Permission"
                description:
                  type: string
                  example: ''
                color:
                  type: string
                  example: "#123123"
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Collection"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsUpdate
  "/collections.add_user":
    post:
      tags:
      - Collections
      summary: Add a collection user
      description: This method allows you to add a user membership to the specified
        collection.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                userId:
                  type: string
                  format: uuid
                permission:
                  "$ref": "#/components/schemas/Permission"
              required:
              - id
              - userId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                      memberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Membership"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsAddUser
  "/collections.remove_user":
    post:
      tags:
      - Collections
      summary: Remove a collection user
      description: This method allows you to remove a user from the specified collection.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Identifier for the collection
                  format: uuid
                userId:
                  type: string
                  format: uuid
              required:
              - id
              - userId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsRemoveUser
  "/collections.memberships":
    post:
      tags:
      - Collections
      summary: List all collection memberships
      description: This method allows you to list a collections individual memberships.
        It's important to note that memberships returned from this endpoint do not
        include group memberships.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - type: object
                properties:
                  id:
                    type: string
                    description: Identifier for the collection
                    format: uuid
                  query:
                    type: string
                    description: Filter memberships by user names
                    example: jenny
                  permission:
                    "$ref": "#/components/schemas/Permission"
                required:
                - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                      memberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Membership"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsMemberships
  "/collections.add_group":
    post:
      tags:
      - Collections
      summary: Add a group to a collection
      description: This method allows you to give all members in a group access to
        a collection.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                groupId:
                  type: string
                  format: uuid
                permission:
                  "$ref": "#/components/schemas/Permission"
              required:
              - id
              - groupId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      collectionGroupMemberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/CollectionGroupMembership"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsAddGroup
  "/collections.remove_group":
    post:
      tags:
      - Collections
      summary: Remove a collection group
      description: This method allows you to revoke all members in a group access
        to a collection. Note that members of the group may still retain access through
        other groups or individual memberships.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Identifier for the collection
                  format: uuid
                groupId:
                  type: string
                  format: uuid
              required:
              - id
              - groupId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsRemoveGroup
  "/collections.group_memberships":
    post:
      tags:
      - Collections
      summary: List all collection group members
      description: This method allows you to list a collections group memberships.
        This is the list of groups that have been given access to the collection.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - type: object
                properties:
                  id:
                    type: string
                    description: Identifier for the collection
                    format: uuid
                  query:
                    type: string
                    description: Filter memberships by group names
                    example: developers
                  permission:
                    "$ref": "#/components/schemas/Permission"
                required:
                - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      groups:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Group"
                      collectionGroupMemberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/CollectionGroupMembership"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsGroupMemberships
  "/collections.delete":
    post:
      tags:
      - Collections
      summary: Delete a collection
      description: Delete a collection and all of its documents. This action can’t
        be undone so please be careful.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsDelete
  "/collections.export":
    post:
      tags:
      - Collections
      summary: Export a collection
      description: Triggers a bulk export of the collection in markdown format and
        their attachments. If documents are nested then they will be nested in folders
        inside the zip file. The endpoint returns a `FileOperation` that can be queried
        to track the progress of the export and get the url for the final file.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                format:
                  type: string
                  enum:
                  - outline-markdown
                  - json
                  - html
                id:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      fileOperation:
                        "$ref": "#/components/schemas/FileOperation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsExport
  "/collections.export_all":
    post:
      tags:
      - Collections
      summary: Export all collections
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                format:
                  type: string
                  enum:
                  - outline-markdown
                  - json
                  - html
      description: Triggers a bulk export of all documents in and their attachments.
        The endpoint returns a `FileOperation` that can be queried through the fileOperations
        endpoint to track the progress of the export and get the url for the final
        file.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      fileOperation:
                        "$ref": "#/components/schemas/FileOperation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: collectionsExportAll
  "/comments.create":
    post:
      tags:
      - Comments
      summary: Create a comment
      description: Create a comment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                documentId:
                  type: string
                  format: uuid
                parentCommentId:
                  type: string
                  format: uuid
                data:
                  type: object
                  description: The body of the comment.
                text:
                  type: string
                  description: The body of the comment in markdown.
                  example: Sounds great
              required:
              - documentId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Comment"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: commentsCreate
  "/comments.info":
    post:
      tags:
      - Comments
      summary: Retrieve a comment
      description: Retrieve a comment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                includeAnchorText:
                  type: boolean
                  description: Include the document text that the comment is anchored
                    to, if any
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Comment"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: commentsInfo
  "/comments.update":
    post:
      tags:
      - Comments
      summary: Update a comment
      description: Update a comment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                data:
                  type: object
              required:
              - id
              - data
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Comment"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: commentsUpdate
  "/comments.delete":
    post:
      tags:
      - Comments
      summary: Delete a comment
      description: Delete a comment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: commentsDelete
  "/comments.list":
    post:
      tags:
      - Comments
      summary: List all comments
      description: This method will list all comments matching the given properties.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  documentId:
                    type: string
                    format: uuid
                    description: Filter to a specific document
                  collectionId:
                    type: string
                    format: uuid
                    description: Filter to a specific collection
                  includeAnchorText:
                    type: boolean
                    description: Include the document text that the comment is anchored
                      to, if any
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Comment"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: commentsList
  "/documents.info":
    post:
      tags:
      - Documents
      summary: Retrieve a document
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                shareId:
                  type: string
                  format: uuid
                  description: Unique identifier for a document share, a shareId may
                    be used in place of a document UUID
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsInfo
  "/documents.import":
    post:
      tags:
      - Documents
      summary: Import a file as a document
      description: This method allows you to create a new document by importing an
        existing file. By default a document is set to the collection root. If you
        want to create a nested/child document, you should pass parentDocumentId to
        set the parent document.
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: object
                  description: Plain text, markdown, docx, csv, tsv, and html format
                    are supported.
                collectionId:
                  type: string
                  format: uuid
                parentDocumentId:
                  type: string
                  format: uuid
                template:
                  type: boolean
                publish:
                  type: boolean
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsImport
  "/documents.export":
    post:
      tags:
      - Documents
      summary: Export a document as markdown
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: string
                    description: The document content in Markdown formatting
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsExport
  "/documents.list":
    post:
      tags:
      - Documents
      summary: List all documents
      description: This method will list all published documents and draft documents
        belonging to the current user.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  collectionId:
                    type: string
                    format: uuid
                    description: Optionally filter to a specific collection
                  userId:
                    type: string
                    format: uuid
                  backlinkDocumentId:
                    type: string
                    format: uuid
                  parentDocumentId:
                    type: string
                    format: uuid
                  template:
                    type: boolean
                    description: Optionally filter to only templates
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsList
  "/documents.drafts":
    post:
      tags:
      - Documents
      summary: List all draft documents
      description: This method will list all draft documents belonging to the current
        user.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  collectionId:
                    type: string
                    description: A collection to search within
                    format: uuid
                  dateFilter:
                    type: string
                    description: Any documents that have not been updated within the
                      specified period will be filtered out
                    example: month
                    enum:
                    - day
                    - week
                    - month
                    - year
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsDrafts
  "/documents.viewed":
    post:
      tags:
      - Documents
      summary: List all recently viewed documents
      description: This method will list all documents recently viewed by the current
        user.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsViewed
  "/documents.answerQuestion":
    post:
      tags:
      - Documents
      summary: Query documents with natural language
      description: This method allows asking direct questions of your documents –
        where possible an answer will be provided. Search results will be restricted
        to those accessible by the current access token. Note that "AI answers" must
        be enabled for the workspace.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - type: object
                properties:
                  query:
                    type: string
                    example: What is our holiday policy?
                  userId:
                    type: string
                    description: Any documents that have not been edited by the user
                      identifier will be filtered out
                    format: uuid
                  collectionId:
                    type: string
                    description: A collection to search within
                    format: uuid
                  documentId:
                    type: string
                    description: A document to search within
                    format: uuid
                  statusFilter:
                    type: string
                    description: Any documents that are not in the specified status
                      will be filtered out
                    enum:
                    - draft
                    - archived
                    - published
                  dateFilter:
                    type: string
                    description: Any documents that have not been updated within the
                      specified period will be filtered out
                    enum:
                    - day
                    - week
                    - month
                    - year
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  documents:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
                  search:
                    "$ref": "#/components/schemas/SearchResult"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsAnswerquestion
  "/documents.search":
    post:
      tags:
      - Documents
      summary: Search all documents
      description: This methods allows you to search your teams documents with keywords.
        Note that search results will be restricted to those accessible by the current
        access token.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - type: object
                properties:
                  query:
                    type: string
                    example: hiring
                  userId:
                    type: string
                    description: Any documents that have not been edited by the user
                      identifier will be filtered out
                    format: uuid
                  collectionId:
                    type: string
                    description: A collection to search within
                    format: uuid
                  documentId:
                    type: string
                    description: A document to search within
                    format: uuid
                  statusFilter:
                    type: string
                    description: Any documents that are not in the specified status
                      will be filtered out
                    example: published
                    enum:
                    - draft
                    - archived
                    - published
                  dateFilter:
                    type: string
                    description: Any documents that have not been updated within the
                      specified period will be filtered out
                    example: month
                    enum:
                    - day
                    - week
                    - month
                    - year
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        context:
                          type: string
                          description: A short snippet of context from the document
                            that includes the search query.
                          example: At Acme Inc our hiring practices are inclusive
                        ranking:
                          type: number
                          description: The ranking used to order search results based
                            on relevance.
                          format: float
                          example: 1.1844109
                        document:
                          "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsSearch
  "/documents.create":
    post:
      tags:
      - Documents
      summary: Create a document
      description: This method allows you to create or publish a new document. By
        default a document is set to the collection root. If you want to create a
        nested/child document, you should pass parentDocumentId to set the parent
        document.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: Welcome to Acme Inc
                text:
                  type: string
                  description: The body of the document in markdown
                collectionId:
                  type: string
                  format: uuid
                parentDocumentId:
                  type: string
                  format: uuid
                templateId:
                  type: string
                  format: uuid
                template:
                  type: boolean
                  description: Whether this document should be considered to be a
                    template.
                publish:
                  type: boolean
                  description: Whether this document should be immediately published
                    and made visible to other team members.
              required:
              - title
              - collectionId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsCreate
  "/documents.update":
    post:
      tags:
      - Documents
      summary: Update a document
      description: This method allows you to modify an already created document
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                title:
                  type: string
                  description: The title of the document.
                text:
                  type: string
                  description: The body of the document in markdown.
                append:
                  type: boolean
                  description: If true the text field will be appended to the end
                    of the existing document, rather than the default behavior of
                    replacing it. This is potentially useful for things like logging
                    into a document.
                publish:
                  type: boolean
                  description: Whether this document should be published and made
                    visible to other team members, if a draft
                done:
                  type: boolean
                  description: Whether the editing session has finished, this will
                    trigger any notifications. This property will soon be deprecated.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsUpdate
  "/documents.templatize":
    post:
      tags:
      - Documents
      summary: Create a template from a document
      description: This method allows you to createa new template using an existing
        document as the basis
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsTemplatize
  "/documents.unpublish":
    post:
      tags:
      - Documents
      summary: Unpublish a document
      description: Unpublishing a document moves it back to a draft status and out
        of the collection.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsUnpublish
  "/documents.move":
    post:
      tags:
      - Documents
      summary: Move a document
      description: Move a document to a new location or collection. If no parent document
        is provided, the document will be moved to the collection root.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                collectionId:
                  type: string
                  format: uuid
                parentDocumentId:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      documents:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Document"
                      collections:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Collection"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsMove
  "/documents.archive":
    post:
      tags:
      - Documents
      summary: Archive a document
      description: Archiving a document allows outdated information to be moved out
        of sight whilst retaining the ability to optionally search and restore it
        later.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsArchive
  "/documents.restore":
    post:
      tags:
      - Documents
      summary: Restore a document
      description: If a document has been archived or deleted, it can be restored.
        Optionally a revision can be passed to restore the document to a previous
        point in time.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                revisionId:
                  type: string
                  format: uuid
                  description: Identifier for the revision to restore to.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Document"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsRestore
  "/documents.delete":
    post:
      tags:
      - Documents
      summary: Delete a document
      description: Deleting a document moves it to the trash. If not restored within
        30 days it is permenantly deleted.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                permanent:
                  type: boolean
                  example: false
                  description: If set to true the document will be destroyed with
                    no way to recover rather than moved to the trash.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsDelete
  "/documents.users":
    post:
      tags:
      - Documents
      summary: List document users
      description: All users with access to a document. To list only users with direct
        membership to the document use `documents.memberships`
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                query:
                  type: string
                  description: If set, will filter the results by user name.
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/User"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsUsers
  "/documents.memberships":
    post:
      tags:
      - Documents
      summary: List document memberships
      description: Users with direct membership to a document. To list all users with
        access to a document use `documents.users`.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  example: hDYep1TPAM
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                query:
                  type: string
                  description: If set, will filter the results by user name
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                      memberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Membership"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsMemberships
  "/documents.add_user":
    post:
      tags:
      - Documents
      summary: Add a document user
      description: This method allows you to add a user membership to the specified
        document.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                userId:
                  type: string
                  format: uuid
                permission:
                  "$ref": "#/components/schemas/Permission"
              required:
              - id
              - userId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                      memberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Membership"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsAddUser
  "/documents.remove_user":
    post:
      tags:
      - Documents
      summary: Remove a document user
      description: This method allows you to remove a user membership from the specified
        document.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the document. Either the UUID
                    or the urlId is acceptable.
                userId:
                  type: string
                  format: uuid
              required:
              - id
              - userId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: documentsRemoveUser
  "/events.list":
    post:
      tags:
      - Events
      summary: List all events
      description: Events are an audit trail of important events that happen in the
        knowledge base.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  name:
                    type: string
                    description: Filter to a specific event, e.g. "collections.create".
                      Event names are in the format "objects.verb"
                  actorId:
                    type: string
                    format: uuid
                    description: Filter to events performed by the selected user
                  documentId:
                    type: string
                    format: uuid
                    description: Filter to events performed in the selected document
                  collectionId:
                    type: string
                    format: uuid
                    description: Filter to events performed in the selected collection
                  auditLog:
                    type: boolean
                    description: Whether to return detailed events suitable for an
                      audit log. Without this flag less detailed event types will
                      be returned.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Event"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: eventsList
  "/fileOperations.info":
    post:
      tags:
      - FileOperations
      summary: Retrieve a file operation
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the file operation.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/FileOperation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: fileoperationsInfo
  "/fileOperations.delete":
    post:
      tags:
      - FileOperations
      summary: Delete a file operation
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the file operation.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: fileoperationsDelete
  "/fileOperations.redirect":
    post:
      tags:
      - FileOperations
      summary: Retrieve the file
      description: Load the resulting file from where it is stored based on the id.
        A temporary, signed url with embedded credentials is generated on demand.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the file operation.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: fileoperationsRedirect
  "/fileOperations.list":
    post:
      tags:
      - FileOperations
      summary: List all file operations
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  type:
                    type: string
                    description: The type of fileOperation
                    example: export
                    enum:
                    - export
                    - import
                required:
                - type
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/FileOperation"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: fileoperationsList
  "/groups.info":
    post:
      tags:
      - Groups
      summary: Retrieve a group
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the group.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Group"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsInfo
  "/groups.list":
    post:
      tags:
      - Groups
      summary: List all groups
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  userId:
                    type: string
                    format: uuid
                    description: Filter to groups including a specific user
                  externalId:
                    type: string
                    format: uuid
                    description: Filter to groups matching an external ID
                  query:
                    type: string
                    format: uuid
                    description: Filter to groups matching a search query
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      groups:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Group"
                      groupMemberships:
                        type: array
                        description: A preview of memberships in the group, note that
                          this is not all memberships which can be queried from `groups.memberships`.
                        items:
                          "$ref": "#/components/schemas/GroupMembership"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsList
  "/groups.create":
    post:
      tags:
      - Groups
      summary: Create a group
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: Designers
              required:
              - name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Group"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsCreate
  "/groups.update":
    post:
      tags:
      - Groups
      summary: Update a group
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                  example: Designers
              required:
              - id
              - name
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Group"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsUpdate
  "/groups.delete":
    post:
      tags:
      - Groups
      summary: Delete a group
      description: Deleting a group will cause all of its members to lose access to
        any collections the group has previously been added to. This action can’t
        be undone so please be careful.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsDelete
  "/groups.memberships":
    post:
      tags:
      - Groups
      summary: List all group members
      description: List and filter all the members in a group.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - type: object
                properties:
                  id:
                    type: string
                    description: Group id
                    example: a32c2ee6-fbde-4654-841b-0eabdc71b812
                  query:
                    type: string
                    description: Filter memberships by user names
                    example: jenny
                required:
                - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                      groupMemberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/GroupMembership"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsMemberships
  "/groups.add_user":
    post:
      tags:
      - Groups
      summary: Add a group member
      description: This method allows you to add a user to the specified group.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                userId:
                  type: string
                  format: uuid
              required:
              - id
              - userId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                      groups:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Group"
                      groupMemberships:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Membership"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsAddUser
  "/groups.remove_user":
    post:
      tags:
      - Groups
      summary: Remove a group member
      description: This method allows you to remove a user from the group.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Identifier for the collection
                  format: uuid
                userId:
                  type: string
                  format: uuid
              required:
              - id
              - userId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      groups:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Group"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: groupsRemoveUser
  "/revisions.info":
    post:
      tags:
      - Revisions
      summary: Retrieve a revision
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the revision.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Revision"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: revisionsInfo
  "/revisions.list":
    post:
      tags:
      - Revisions
      summary: List all revisions
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  documentId:
                    type: string
                    format: uuid
                    description: The document ID to retrieve revisions for
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Revision"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: revisionsList
  "/shares.info":
    post:
      tags:
      - Shares
      summary: Retrieve a share object
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the share.
                  format: uuid
                documentId:
                  type: string
                  description: Unique identifier for a document. One of id or documentId
                    must be provided.
                  format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Share"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: sharesInfo
  "/shares.list":
    post:
      tags:
      - Shares
      summary: List all shares
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  query:
                    type: string
                    format: uuid
                    description: Filter to shared documents matching a search query
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Share"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: sharesList
  "/shares.create":
    post:
      tags:
      - Shares
      summary: Create a share
      description: Creates a new share link that can be used by to access a document.
        If you request multiple shares for the same document with the same API key,
        the same share object will be returned. By default all shares are unpublished.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                documentId:
                  type: string
                  format: uuid
              required:
              - documentId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Share"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: sharesCreate
  "/shares.update":
    post:
      tags:
      - Shares
      summary: Update a share
      description: Allows changing an existing shares published status, which removes
        authentication and makes it available to anyone with the link.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                published:
                  type: boolean
              required:
              - id
              - published
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Share"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: sharesUpdate
  "/shares.revoke":
    post:
      tags:
      - Shares
      summary: Revoke a share
      description: Makes the share link inactive so that it can no longer be used
        to access the document.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: sharesRevoke
  "/stars.create":
    post:
      tags:
      - Stars
      summary: Create a star
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                documentId:
                  type: string
                  format: uuid
                collectionId:
                  type: string
                  format: uuid
                index:
                  type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Star"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: starsCreate
  "/stars.list":
    post:
      tags:
      - Stars
      summary: List all stars
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/Pagination"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      stars:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Star"
                      documents:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Document"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: starsList
  "/stars.update":
    post:
      tags:
      - Stars
      summary: Update a stars index
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                index:
                  type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Star"
                  policies:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Policy"
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: starsUpdate
  "/stars.delete":
    post:
      tags:
      - Stars
      summary: Delete a star
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          "$ref": "#/components/responses/Validation"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: starsDelete
  "/users.invite":
    post:
      tags:
      - Users
      summary: Invite users
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                invites:
                  type: array
                  items:
                    "$ref": "#/components/schemas/Invite"
              required:
              - invites
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      sent:
                        type: array
                        items:
                          "$ref": "#/components/schemas/Invite"
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersInvite
  "/users.info":
    post:
      tags:
      - Users
      summary: Retrieve a user
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the user.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/User"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersInfo
  "/users.list":
    post:
      tags:
      - Users
      summary: List all users
      description: List and filter all the users in the team
      requestBody:
        content:
          application/json:
            schema:
              allOf:
              - "$ref": "#/components/schemas/Pagination"
              - "$ref": "#/components/schemas/Sorting"
              - type: object
                properties:
                  query:
                    type: string
                    example: jane
                  emails:
                    type: array
                    description: Array of emails
                    items:
                      type: string
                    example:
                    - <EMAIL>
                    - <EMAIL>
                  filter:
                    type: string
                    description: The status to filter by
                    enum:
                    - all
                    - invited
                    - active
                    - suspended
                  role:
                    "$ref": "#/components/schemas/UserRole"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/User"
                  pagination:
                    "$ref": "#/components/schemas/Pagination"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersList
  "/users.update":
    post:
      tags:
      - Users
      summary: Update a user
      description: Update a users name or avatar. If no `id` is passed then the user
        associated with the authentication will be updated by default.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                language:
                  type: string
                  format: BCP47
                avatarUrl:
                  type: string
                  format: uri
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/User"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersUpdate
  "/users.update_role":
    post:
      tags:
      - Users
      summary: Change a users role
      description: Change the role of a user, only available to admin authorization.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the user.
                  format: uuid
                role:
                  "$ref": "#/components/schemas/UserRole"
              required:
              - id
              - role
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/User"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersUpdateRole
  "/users.suspend":
    post:
      tags:
      - Users
      summary: Suspend a user
      description: Suspending a user prevents the user from signing in. Users that
        are suspended are also not counted against billing totals in the hosted version.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the user.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/User"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersSuspend
  "/users.activate":
    post:
      tags:
      - Users
      summary: Activate a user
      description: Activating a previously suspended user allows them to signin again.
        Users that are activated will cause billing totals to be re-calculated in
        the hosted version.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the user.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/User"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersActivate
  "/users.delete":
    post:
      tags:
      - Users
      summary: Delete a user
      description: Deleting a user removes the object entirely. In almost every circumstance
        it is preferable to suspend a user, as a deleted user can be recreated by
        signing in with SSO again.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the user.
                  format: uuid
              required:
              - id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '404':
          "$ref": "#/components/responses/NotFound"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: usersDelete
  "/views.list":
    post:
      tags:
      - Views
      summary: List all views
      description: List all users that have viewed a document and the overall view
        count.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                documentId:
                  type: string
                  format: uuid
                  description: The document ID to retrieve views for
              required:
              - documentId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/View"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
        '429':
          "$ref": "#/components/responses/RateLimited"
      operationId: viewsList
  "/views.create":
    post:
      tags:
      - Views
      summary: Create a view
      description: Creates a new view for a document. This is documented in the interests
        of thoroughness however it is recommended that views are not created from
        outside of the Outline UI.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                documentId:
                  type: string
                  format: uuid
              required:
              - documentId
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/View"
        '401':
          "$ref": "#/components/responses/Unauthenticated"
        '403':
          "$ref": "#/components/responses/Unauthorized"
      operationId: viewsCreate
components:
  schemas:
    Permission:
      type: string
      enum:
      - read
      - read_write
    Attachment:
      type: object
      properties:
        contentType:
          type: string
          example: image/png
        size:
          type: number
        name:
          type: string
        url:
          type: string
          format: uri
        documentId:
          type: string
          description: Identifier for the associated document, if any.
          format: uuid
    Pagination:
      type: object
      properties:
        offset:
          type: number
        limit:
          type: number
          example: 25
    Sorting:
      type: object
      properties:
        sort:
          type: string
          example: updatedAt
        direction:
          type: string
          example: DESC
          enum:
          - ASC
          - DESC
    NavigationNode:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the document.
          format: uuid
        title:
          type: string
        url:
          type: string
        children:
          type: array
          items:
            "$ref": "#/components/schemas/NavigationNode"
    Auth:
      type: object
      properties:
        user:
          "$ref": "#/components/schemas/User"
        team:
          "$ref": "#/components/schemas/Team"
    Collection:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        urlId:
          type: string
          description: A short unique identifier that can be used to identify the
            collection instead of the UUID.
          readOnly: true
          example: hDYep1TPAM
        name:
          type: string
          description: The name of the collection.
          example: Human Resources
        description:
          type: string
          description: A description of the collection, may contain markdown formatting
          example: ''
        sort:
          type: object
          description: The sort of documents in the collection. Note that not all
            API responses respect this and it is left as a frontend concern to implement.
          properties:
            field:
              type: string
            direction:
              type: string
              enum:
              - asc
              - desc
        index:
          type: string
          description: The position of the collection in the sidebar
          example: P
        color:
          type: string
          description: 'A color representing the collection, this is used to help
            make collections more identifiable in the UI. It should be in HEX format
            including the #'
          example: "#123123"
        icon:
          type: string
          description: A string that represents an icon in the outline-icons package
        permission:
          "$ref": "#/components/schemas/Permission"
        sharing:
          type: boolean
          description: Whether public document sharing is enabled in this collection
          default: false
        createdAt:
          type: string
          description: The date and time that this object was created
          readOnly: true
          format: date-time
        updatedAt:
          type: string
          description: The date and time that this object was last changed
          readOnly: true
          format: date-time
        deletedAt:
          type: string
          nullable: true
          description: The date and time that this object was deleted
          readOnly: true
          format: date-time
        archivedAt:
          type: string
          nullable: true
          description: The date and time that this object was archived
          readOnly: true
          format: date-time
        archivedBy:
          "$ref": "#/components/schemas/User"
    Comment:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        data:
          type: object
          description: The editor data representing this comment.
        documentId:
          type: string
          description: Identifier for the document this is related to.
          format: uuid
        parentCommentId:
          type: string
          description: Identifier for the comment this is a child of, if any.
          format: uuid
        createdAt:
          type: string
          description: The date and time that this object was created
          readOnly: true
          format: date-time
        createdBy:
          "$ref": "#/components/schemas/User"
        updatedAt:
          type: string
          description: The date and time that this object was last changed
          readOnly: true
          format: date-time
        updatedBy:
          "$ref": "#/components/schemas/User"
        anchorText:
          type: string
          description: The document text that the comment is anchored to, only included
            if includeAnchorText=true.
          readOnly: true
    Document:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        collectionId:
          type: string
          description: Identifier for the associated collection.
          format: uuid
        parentDocumentId:
          type: string
          description: Identifier for the document this is a child of, if any.
          format: uuid
        title:
          type: string
          description: The title of the document.
          example: "\U0001F389 Welcome to Acme Inc"
        fullWidth:
          type: boolean
          description: Whether this document should be displayed in a full-width view.
        emoji:
          type: string
          description: An emoji associated with the document.
          example: "\U0001F389"
        text:
          type: string
          description: The text content of the document, contains markdown formatting
          example: "…"
        urlId:
          type: string
          description: A short unique ID that can be used to identify the document
            as an alternative to the UUID
          example: hDYep1TPAM
        collaborators:
          type: array
          items:
            "$ref": "#/components/schemas/User"
        pinned:
          type: boolean
          description: Whether this document is pinned in the collection
        template:
          type: boolean
          description: Whether this document is a template
        templateId:
          type: string
          description: Unique identifier for the template this document was created
            from, if any
          format: uuid
        revision:
          type: number
          description: A number that is auto incrementing with every revision of the
            document that is saved
          readOnly: true
        createdAt:
          type: string
          description: The date and time that this object was created
          readOnly: true
          format: date-time
        createdBy:
          "$ref": "#/components/schemas/User"
        updatedAt:
          type: string
          description: The date and time that this object was last changed
          readOnly: true
          format: date-time
        updatedBy:
          "$ref": "#/components/schemas/User"
        publishedAt:
          type: string
          nullable: true
          description: The date and time that this object was published
          readOnly: true
          format: date-time
        archivedAt:
          type: string
          description: The date and time that this object was archived
          readOnly: true
          format: date-time
        deletedAt:
          type: string
          nullable: true
          description: The date and time that this object was deleted
          readOnly: true
          format: date-time
    Event:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        name:
          type: string
          example: documents.create
          readOnly: true
        modelId:
          type: string
          description: Identifier for the object this event is associated with when
            it is not one of document, collection, or user.
          format: uuid
          readOnly: true
        actorId:
          type: string
          description: The user that performed the action.
          format: uuid
          readOnly: true
        actorIpAddress:
          type: string
          description: The ip address the action was performed from. This field is
            only returned when the `auditLog` boolean is true.
          example: *************
          readOnly: true
        collectionId:
          type: string
          format: uuid
          description: Identifier for the associated collection, if any
          readOnly: true
        documentId:
          type: string
          format: uuid
          description: Identifier for the associated document, if any
          readOnly: true
        createdAt:
          type: string
          description: The date and time that this event was created
          readOnly: true
          format: date-time
        data:
          type: object
          example:
            name: Equipment list
          description: Additional unstructured data associated with the event
          readOnly: true
        actor:
          "$ref": "#/components/schemas/User"
    Error:
      type: object
      properties:
        ok:
          type: boolean
          example: false
        error:
          type: string
        message:
          type: string
        status:
          type: number
        data:
          type: object
    FileOperation:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        type:
          type: string
          example: export
          description: The type of file operation.
          readOnly: true
          enum:
          - import
          - export
        state:
          type: string
          description: The state of the file operation.
          example: complete
          readOnly: true
          enum:
          - creating
          - uploading
          - complete
          - error
          - expired
        collection:
          allOf:
          - nullable: true
          - "$ref": "#/components/schemas/Collection"
        user:
          "$ref": "#/components/schemas/User"
        size:
          type: number
          description: The size of the resulting file in bytes
          readOnly: true
          example: 2048
        createdAt:
          type: string
          description: The date and time that this object was created
          readOnly: true
          format: date-time
    Group:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        name:
          type: string
          description: The name of this group.
          example: Engineering
        memberCount:
          type: number
          description: The number of users that are members of the group
          example: 11
          readOnly: true
        createdAt:
          type: string
          description: The date and time that this object was created
          readOnly: true
          format: date-time
        updatedAt:
          type: string
          description: The date and time that this object was last changed
          readOnly: true
          format: date-time
    Share:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        documentTitle:
          type: string
          description: Title of the shared document.
          example: React best practices
          readOnly: true
        documentUrl:
          type: string
          format: uri
          description: URL of the original document.
          readOnly: true
        url:
          type: string
          format: uri
          description: URL of the publicly shared document.
          readOnly: true
        published:
          type: boolean
          example: false
          description: If true the share can be loaded without a user account.
        includeChildDocuments:
          type: boolean
          example: true
          description: If to also give permission to view documents nested beneath
            this one.
        createdAt:
          type: string
          format: date-time
          description: Date and time when this share was created
          readOnly: true
        createdBy:
          "$ref": "#/components/schemas/User"
        updatedAt:
          type: string
          format: date-time
          description: Date and time when this share was edited
          readOnly: true
        lastAccessedAt:
          type: string
          format: date-time
          description: Date and time when this share was last viewed
          readOnly: true
    Star:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        index:
          type: string
          description: Index of the star in the list of stars.
        documentId:
          type: string
          description: Unique identifier for the starred document.
          readOnly: true
          format: uuid
        collectionId:
          type: string
          description: Unique identifier for the starred collection.
          readOnly: true
          format: uuid
        documentUrl:
          type: string
          format: uri
          description: URL of the original document.
          readOnly: true
        createdAt:
          type: string
          format: date-time
          description: Date and time when this star was created
          readOnly: true
    Revision:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        documentId:
          type: string
          description: Identifier for the associated document.
          readOnly: true
          format: uuid
        title:
          type: string
          description: Title of the document.
          readOnly: true
        text:
          type: string
          description: Body of the document, may contain markdown formatting
          readOnly: true
        createdAt:
          type: string
          format: date-time
          description: Date and time when this revision was created
          readOnly: true
        createdBy:
          "$ref": "#/components/schemas/User"
    Team:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        name:
          type: string
          description: The name of this team, it is usually auto-generated when the
            first SSO connection is made but can be changed if neccessary.
        avatarUrl:
          type: string
          format: uri
          description: The URL for the image associated with this team, it will be
            displayed in the team switcher and in the top left of the knowledge base
            along with the name.
        sharing:
          type: boolean
          description: Whether this team has share links globally enabled. If this
            value is false then all sharing UI and APIs are disabled.
        defaultCollectionId:
          type: string
          description: If set then the referenced collection is where users will be
            redirected to after signing in instead of the Home screen
          format: uuid
        defaultUserRole:
          "$ref": "#/components/schemas/UserRole"
        memberCollectionCreate:
          type: boolean
          description: Whether members are allowed to create new collections. If false
            then only admins can create collections.
        documentEmbeds:
          type: boolean
          description: Whether this team has embeds in documents globally enabled.
            It can be disabled to reduce potential data leakage to third parties.
        collaborativeEditing:
          type: boolean
          description: Whether this team has collaborative editing in documents globally
            enabled.
        inviteRequired:
          type: boolean
          description: Whether an invite is required to join this team, if false users
            may join with a linked SSO provider.
        allowedDomains:
          type: array
          items:
            type: string
            description: A hostname that user emails are restricted to
        guestSignin:
          type: boolean
          description: Whether this team has guest signin enabled. Guests can signin
            with an email address and are not required to have a Google Workspace/Slack
            SSO account once invited.
        subdomain:
          type: string
          description: Represents the subdomain at which this team's knowledge base
            can be accessed.
        url:
          type: string
          description: The fully qualified URL at which this team's knowledge base
            can be accessed.
          readOnly: true
          format: uri
    User:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
          format: uuid
        name:
          type: string
          description: The name of this user, it is migrated from Slack or Google
            Workspace when the SSO connection is made but can be changed if neccessary.
          example: Jane Doe
        avatarUrl:
          type: string
          format: uri
          description: The URL for the image associated with this user, it will be
            displayed in the application UI and email notifications.
        email:
          type: string
          description: The email associated with this user, it is migrated from Slack
            or Google Workspace when the SSO connection is made but can be changed
            if neccessary.
          format: email
          readOnly: true
        role:
          "$ref": "#/components/schemas/UserRole"
        isSuspended:
          type: boolean
          description: Whether this user has been suspended.
          readOnly: true
        lastActiveAt:
          type: string
          description: The last time this user made an API request, this value is
            updated at most every 5 minutes.
          readOnly: true
          format: date-time
        createdAt:
          type: string
          description: The date and time that this user first signed in or was invited
            as a guest.
          readOnly: true
          format: date-time
    Invite:
      type: object
      properties:
        name:
          type: string
          description: The full name of the user being invited
        email:
          type: string
          description: The email address to invite
        role:
          "$ref": "#/components/schemas/UserRole"
    UserRole:
      type: string
      enum:
      - admin
      - member
      - viewer
      - guest
    CollectionStatus:
      type: string
      enum:
      - archived
    Membership:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
        userId:
          type: string
          description: Identifier for the associated user.
          readOnly: true
          format: uuid
        collectionId:
          type: string
          description: Identifier for the associated collection.
          readOnly: true
          format: uuid
        permission:
          "$ref": "#/components/schemas/Permission"
    SearchResult:
      type: object
      properties:
        id:
          type: string
          readOnly: true
          format: uuid
        query:
          type: string
          description: The user-provided search query
          example: What is our hiring policy?
          readOnly: true
        answer:
          type: string
          description: An answer to the query, if possible
          example: Our hiring policy can be summarized as…
          readOnly: true
        source:
          type: string
          example: app
          description: The source of the query
          readOnly: true
          enum:
          - api
          - app
        createdAt:
          type: string
          description: The date and time that this object was created
          readOnly: true
          format: date-time
    Policy:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object this policy references.
          format: uuid
          readOnly: true
        abilities:
          type: object
          properties:
            create:
              type: boolean
            read:
              type: boolean
            update:
              type: boolean
            delete:
              type: boolean
            restore:
              type: boolean
            star:
              type: boolean
            unstar:
              type: boolean
            share:
              type: boolean
            download:
              type: boolean
            pin:
              type: boolean
            unpin:
              type: boolean
            move:
              type: boolean
            archive:
              type: boolean
            unarchive:
              type: boolean
            createChildDocument:
              type: boolean
    GroupMembership:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
        groupId:
          type: string
          description: Identifier for the associated group.
          readOnly: true
          format: uuid
        userId:
          type: string
          description: Identifier for the associated user.
          readOnly: true
          format: uuid
        user:
          "$ref": "#/components/schemas/User"
    CollectionGroupMembership:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
        groupId:
          type: string
          description: Identifier for the associated group.
          readOnly: true
          format: uuid
        collectionId:
          type: string
          description: Identifier for the associated collection.
          readOnly: true
          format: uuid
        permission:
          "$ref": "#/components/schemas/Permission"
    View:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the object.
          readOnly: true
        documentId:
          type: string
          description: Identifier for the associated document.
          readOnly: true
          format: uuid
        firstViewedAt:
          type: string
          description: When the document was first viewed by the user
          readOnly: true
          format: date-time
        lastViewedAt:
          type: string
          description: When the document was last viewed by the user
          readOnly: true
          format: date-time
        count:
          type: number
          description: The number of times the user has viewed the document.
          example: 22
          readOnly: true
        user:
          "$ref": "#/components/schemas/User"
  responses:
    NotFound:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/Error"
    Validation:
      description: The request failed one or more validations.
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/Error"
    Unauthorized:
      description: The current API key is not authorized to perform this action.
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/Error"
    Unauthenticated:
      description: The API key is missing or otherwise invalid.
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/Error"
    RateLimited:
      description: The request was rate limited.
      headers:
        Retry-After:
          "$ref": "#/components/headers/Retry-After"
        RateLimit-Limit:
          "$ref": "#/components/headers/RateLimit-Limit"
        RateLimit-Remaining:
          "$ref": "#/components/headers/RateLimit-Remaining"
        RateLimit-Reset:
          "$ref": "#/components/headers/RateLimit-Reset"
      content:
        application/json:
          schema:
            type: object
            properties:
              ok:
                type: boolean
                example: false
              error:
                type: string
                example: rate_limit_exceeded
              status:
                type: number
                example: 429
  headers:
    Retry-After:
      schema:
        type: integer
      description: Seconds in the future to retry the request, if rate limited.
    RateLimit-Limit:
      schema:
        type: integer
      description: The maximum requests available in the current duration.
    RateLimit-Remaining:
      schema:
        type: integer
      description: How many requests are left in the current duration.
    RateLimit-Reset:
      schema:
        type: string
      description: Timestamp in the future the duration will reset.
  securitySchemes:
    http:
      type: http
      scheme: bearer
      bearerFormat: JWT
