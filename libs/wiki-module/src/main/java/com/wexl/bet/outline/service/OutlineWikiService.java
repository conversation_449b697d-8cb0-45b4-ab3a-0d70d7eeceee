package com.wexl.bet.outline.service;

import com.wexl.bet.outline.dto.CollectionDto;
import com.wexl.bet.outline.dto.DocumentDto;
import com.wexl.bet.outline.dto.OutlineResponse;
import java.util.Collections;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/** Service for interacting with the Outline API. */
@Service
public class OutlineWikiService {
  private static final Logger log = LoggerFactory.getLogger(OutlineWikiService.class);

  private final RestTemplate restTemplate;
  private final String apiBaseUrl;
  private final String apiKey;

  /**
   * Constructor for OutlineWikiService.
   *
   * @param restTemplate The RestTemplate to use for HTTP requests
   * @param apiBaseUrl The base URL for the Outline API
   * @param apiKey The API key for authentication
   */
  public OutlineWikiService(
      RestTemplate restTemplate,
      @Value("${outline.api.base-url:https://app.getoutline.com/api}") String apiBaseUrl,
      @Value("${outline.api.key:ol_api_Ww371uSBaIVJWoeD0qkGrQHLLNJDlGOJ18f1Yk}") String apiKey) {
    this.restTemplate = restTemplate;
    this.apiBaseUrl = apiBaseUrl;
    this.apiKey = apiKey;
  }

  /**
   * Creates a new collection in Outline.
   *
   * @param request The collection creation request
   * @return The created collection
   */
  public OutlineResponse<CollectionDto.Collection> createCollection(
      CollectionDto.CreateRequest request) {
    try {
      String url = apiBaseUrl + "/collections.create";
      HttpEntity<CollectionDto.CreateRequest> entity = new HttpEntity<>(request, createHeaders());

      ResponseEntity<OutlineResponse<CollectionDto.Collection>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      return response.getBody();
    } catch (RestClientException e) {
      log.error("Error creating collection: {}", e.getMessage(), e);
      return OutlineResponse.error("Error creating collection: " + e.getMessage());
    }
  }

  /**
   * Gets a collection by its UUID.
   *
   * @param uuid The UUID of the collection
   * @return The collection
   */
  public OutlineResponse<CollectionDto.Collection> getCollectionByUuid(String uuid) {
    try {
      String url = apiBaseUrl + "/collections.info";
      CollectionDto.InfoRequest request = new CollectionDto.InfoRequest(uuid);
      HttpEntity<CollectionDto.InfoRequest> entity = new HttpEntity<>(request, createHeaders());

      ResponseEntity<OutlineResponse<CollectionDto.Collection>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      return response.getBody();
    } catch (RestClientException e) {
      log.error("Error getting collection by UUID: {}", e.getMessage(), e);
      return OutlineResponse.error("Error getting collection by UUID: " + e.getMessage());
    }
  }

  /**
   * Gets a collection by its name.
   *
   * @param name The name of the collection
   * @return The collection, or null if not found
   */
  public OutlineResponse<CollectionDto.Collection> getCollectionByName(String name) {
    try {
      // First, list all collections
      String url = apiBaseUrl + "/collections.list";
      HttpEntity<Void> entity = new HttpEntity<>(createHeaders());

      ResponseEntity<OutlineResponse<List<CollectionDto.Collection>>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      OutlineResponse<List<CollectionDto.Collection>> body = response.getBody();
      if (body != null && body.ok() && body.data() != null) {
        // Find the collection with the matching name
        return body.data().stream()
            .filter(collection -> name.equals(collection.name()))
            .findFirst()
            .map(OutlineResponse::success)
            .orElse(OutlineResponse.error("Collection not found with name: " + name));
      } else {
        return OutlineResponse.error("Error listing collections");
      }
    } catch (RestClientException e) {
      log.error("Error getting collection by name: {}", e.getMessage(), e);
      return OutlineResponse.error("Error getting collection by name: " + e.getMessage());
    }
  }

  /**
   * Gets document information by its UUID.
   *
   * @param uuid The UUID of the document
   * @return The document information
   */
  public OutlineResponse<DocumentDto.Response> getDocumentInfoByUuid(String uuid) {
    try {
      String url = apiBaseUrl + "/documents.info";
      DocumentDto.InfoRequest request = new DocumentDto.InfoRequest(uuid);
      HttpEntity<DocumentDto.InfoRequest> entity = new HttpEntity<>(request, createHeaders());

      ResponseEntity<OutlineResponse<DocumentDto.Response>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      return response.getBody();
    } catch (RestClientException e) {
      log.error("Error getting document by UUID: {}", e.getMessage(), e);
      return OutlineResponse.error("Error getting document by UUID: " + e.getMessage());
    }
  }

  public OutlineResponse<DocumentDto.DocumentExportResponse> exportDocumentInfoByUuid(String uuid) {
    if (uuid == null || uuid.trim().isEmpty()) {
      return OutlineResponse.error("Document UUID cannot be null or empty");
    }
    try {
      String url = apiBaseUrl + "/documents.export";
      DocumentDto.InfoRequest request = new DocumentDto.InfoRequest(uuid);
      HttpEntity<DocumentDto.InfoRequest> entity = new HttpEntity<>(request, createHeaders());

      ResponseEntity<OutlineResponse<DocumentDto.DocumentExportResponse>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      return response.getBody();
    } catch (RestClientException e) {
      log.error("Error getting document by UUID: {}", e.getMessage(), e);
      return OutlineResponse.error("Error getting document by UUID: " + e.getMessage());
    }
  }

  /**
   * Gets all documents in a collection.
   *
   * @param collectionId The UUID of the collection
   * @return A list of documents in the collection
   */
  public OutlineResponse<List<DocumentDto.NavigationNode>> getDocumentsInCollection(
      String collectionId) {
    try {
      String url = apiBaseUrl + "/collections.documents";

      // Create a simple object with the collection ID
      record CollectionDocumentsRequest(String id) {}
      CollectionDocumentsRequest request = new CollectionDocumentsRequest(collectionId);

      HttpEntity<CollectionDocumentsRequest> entity = new HttpEntity<>(request, createHeaders());

      ResponseEntity<OutlineResponse<List<DocumentDto.NavigationNode>>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      return response.getBody();
    } catch (RestClientException e) {
      log.error("Error getting documents in collection: {}", e.getMessage(), e);
      return OutlineResponse.error("Error getting documents in collection: " + e.getMessage());
    }
  }

  /**
   * Creates a new document in Outline.
   *
   * @param request The document creation request
   * @return The created document
   */
  public OutlineResponse<DocumentDto.Response> createDocument(DocumentDto.CreateRequest request) {
    try {
      String url = apiBaseUrl + "/documents.create";
      HttpEntity<DocumentDto.CreateRequest> entity = new HttpEntity<>(request, createHeaders());

      ResponseEntity<OutlineResponse<DocumentDto.Response>> response =
          restTemplate.exchange(
              url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {});

      return response.getBody();
    } catch (RestClientException e) {
      log.error("Error creating document: {}", e.getMessage(), e);
      return OutlineResponse.error("Error creating document: " + e.getMessage());
    }
  }

  /**
   * Creates the HTTP headers for API requests.
   *
   * @return The HTTP headers
   */
  private HttpHeaders createHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    headers.set("Authorization", "Bearer " + apiKey);
    return headers;
  }
}
