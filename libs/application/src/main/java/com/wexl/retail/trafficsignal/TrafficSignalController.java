package com.wexl.retail.trafficsignal;

import com.wexl.retail.student.exam.ExamService;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TrafficSignalController {

  private final ExamService examService;

  public TrafficSignalController(ExamService examService) {
    this.examService = examService;
  }

  @GetMapping("/subtopic")
  public List<Map<String, Object>> trafficSignal(
      @RequestParam long chapterId,
      @RequestParam long subjectId,
      @RequestParam long examDifficultyLevelId) {
    return examService.trafficSignal(chapterId, subjectId, examDifficultyLevelId);
  }
}
