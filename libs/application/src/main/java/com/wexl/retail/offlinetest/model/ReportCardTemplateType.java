package com.wexl.retail.offlinetest.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ReportCardTemplateType {
  CUSTOM("CUSTOM"),
  CANNED("CANNED"),
  ANSWER_SHEET_TEMPLATE("ANSWER_SHEET_TEMPLATE");

  private final String value;

  public static ReportCardTemplateType fromValue(String value) {
    if (value == null || value.isEmpty()) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (ReportCardTemplateType enumEntry : ReportCardTemplateType.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
