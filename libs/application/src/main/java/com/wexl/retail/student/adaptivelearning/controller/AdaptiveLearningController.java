package com.wexl.retail.student.adaptivelearning.controller;

import com.wexl.retail.student.adaptivelearning.service.AdaptiveLearningService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class AdaptiveLearningController {

  private final AdaptiveLearningService adaptiveLearningService;

  @PostMapping("/exams/{examId}/adaptive-learnings")
  public void analyzeStudentExam(@PathVariable("examId") long examId) {
    adaptiveLearningService.performAdaptiveLearning(examId);
  }
}
