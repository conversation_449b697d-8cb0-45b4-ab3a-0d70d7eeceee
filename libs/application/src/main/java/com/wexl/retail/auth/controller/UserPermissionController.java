package com.wexl.retail.auth.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class UserPermissionController {

  private final UserRepository userRepository;
  private final UserRoleHelper userRoleHelper;
  private final AuthService authService;

  @GetMapping("/orgs/{orgSlug}/{roles}/{userAuthId}/permissions")
  @IsOrgAdmin
  public ResponseEntity<List<String>> getPermissions(@PathVariable String orgSlug) {
    if (!"wexl-internal".equals(authService.getTeacherDetails().getOrganization())) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ProcessingAuth");
    }
    User orgAdmin = userRepository.getOrgAdminForOrganization(orgSlug);
    return ResponseEntity.ok().body(userRoleHelper.getPermissionsForUser(orgAdmin));
  }
}
