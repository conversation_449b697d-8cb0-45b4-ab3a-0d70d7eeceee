package com.wexl.retail.organization;

import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrgDeletionService {
  private final OrganizationRepository organizationRepository;
  private final UserRepository userRepository;

  @Async
  @Transactional
  public void deleteOrg(OrgDto orgDto) {
    for (String org : orgDto.getOrgs()) {
      log.info("Deleting org {}", org);
      var organization = organizationRepository.findBySlug(org);
      if (organization.getDeletedAt() == null) {
        return;
      }
      try {
        var userCount = userRepository.countByOrganization(org);
        log.info("User count {}", userCount);
        organizationRepository.deleteStudentsByOrgSlug(org);
        log.info("Deleted students");
        organizationRepository.updateAdminRole(org);
        log.info("Updated admin role to Teacher");
        organizationRepository.deleteTeacherByOrgSlug(org);
        log.info("Deleted teachers");
        organizationRepository.deleteOrganizationBySlug(org);
        log.info("Deleted org {}");
      } catch (Exception e) {
        log.error("Failed to delete org {}: {}", org, e.getMessage(), e);
        throw e;
      }
    }
  }
}
