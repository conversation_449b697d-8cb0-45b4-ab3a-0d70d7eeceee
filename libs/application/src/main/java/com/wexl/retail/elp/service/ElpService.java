package com.wexl.retail.elp.service;

import static com.wexl.retail.student.exam.migration.ExamMigrationService.MARKS_SCORED;
import static com.wexl.retail.util.Constants.*;
import static java.time.ZoneOffset.UTC;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.elp.dto.*;
import com.wexl.retail.elp.dto.ElpDto.StudentTaskInst;
import com.wexl.retail.elp.repository.Vocabulary;
import com.wexl.retail.elp.repository.WordRepository;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.dto.CurriculumBoard;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.auth.StudentAuthTransformer;
import com.wexl.retail.student.exam.*;
import com.wexl.retail.student.exam.dto.ExamDto;
import com.wexl.retail.student.exam.school.SchoolExamService;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.ElpCounts;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.task.service.TaskInstService;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.dto.PbqDto;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.*;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ElpService {

  private final TestDefinitionRepository testDefinitionRepository;
  private final TaskRepository taskRepository;
  private final ContentService contentService;
  private final StrapiService strapiService;
  private final StudentRepository studentRepository;
  private final ValidationUtils validationUtils;
  private final TaskInstRepository taskInstRepository;
  private final TestDefinitionService testDefinitionService;
  private final ExamFactory examFactory;
  private final ExamTransformer examTransformer;
  private final ExamRepository examRepository;
  private final ExamUtils examUtils;
  private final TeacherRepository teacherRepository;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final TaskInstService taskInstService;
  private final UserService userService;
  private final StudentAuthService studentAuthService;
  private final CurriculumService curriculumService;
  private final OrganizationRepository organizationRepository;
  private final AuthService authService;
  private final UserRepository userRepository;
  private final PasswordEncoder passwordEncoder;
  private final SectionService sectionService;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherOrgsService teacherOrgsService;
  private final SpeechEvaluationService speechEvaluationService;
  private final StudentAuthTransformer studentAuthTransformer;
  private final SchoolExamService schoolExamService;
  private final ExamService examService;
  private static final String BHARAT_ENGLISH_TEST_ORG = "bha215263";

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  private static final String NOT_ATTEMPT_COUNT = "notAttemptedCount";
  private static final String NOT_FOUND = "Elp not found";
  private final String[] sectionNames =
      new String[] {"Listening", "Speaking", "Reading", "Vocabulary", "Grammar"};

  @Value("${app.contentToken}")
  String contentBearerToken;

  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";

  public void initializeElp(String orgSlug, String boardSlug) {
    var board = contentService.getBoardDetailsBySlug(orgSlug, boardSlug);

    List<Task> tasks = new ArrayList<>();
    board
        .grades()
        .forEach(
            grade ->
                grade
                    .subjects()
                    .forEach(
                        subject ->
                            subject
                                .chapters()
                                .forEach(
                                    chapter ->
                                        chapter
                                            .subTopics()
                                            .forEach(
                                                subTopic -> {
                                                  if (Boolean.TRUE.equals(
                                                      validateIfExist(subTopic, orgSlug))) {
                                                    return;
                                                  }
                                                  tasks.add(
                                                      buildTasks(
                                                          board, grade, subject, chapter, subTopic,
                                                          orgSlug));
                                                }))));
    if (!tasks.isEmpty()) {
      taskRepository.saveAll(tasks.stream().distinct().toList());
    }
  }

  private Boolean validateIfExist(BoardsDto.SubTopics subTopic, String orgSlug) {
    return taskRepository.existsByOrgSlugAndSubtopicSlugAndTaskType(
        orgSlug, subTopic.subtopicSlug(), TaskType.ELP);
  }

  private Task buildTasks(
      BoardsDto.Boards board,
      BoardsDto.Grades grade,
      BoardsDto.Subjects subject,
      BoardsDto.Chapters chapter,
      BoardsDto.SubTopics subTopic,
      String orgSlug) {
    var adminTeachers = teacherRepository.getAllAdminsByOrg(orgSlug);
    return Task.builder()
        .boardSlug(board.boardSlug())
        .boardName(board.boardName())
        .gradeSlug(grade.gradeSlug())
        .gradeName(grade.gradeName())
        .orgSlug(orgSlug)
        .subjectSlug(subject.subjectSlug())
        .subjectName(subject.subjectName())
        .chapterName(chapter.chapterName())
        .chapterSlug(chapter.chapterSlug())
        .subtopicSlug(subTopic.subtopicSlug())
        .subtopicName(subTopic.subtopicName())
        .taskType(TaskType.ELP)
        .elpSlug(board.boardSlug() + "-" + chapter.chapterSlug())
        .teacher(adminTeachers.getFirst())
        .build();
  }

  public List<ElpDto.Chapter> getElps(String orgSlug, String gradeSlug, String boardSlug) {
    var org = organizationRepository.findBySlug(orgSlug);
    List<String> boardSlugs = new ArrayList<>();
    if (Objects.isNull(boardSlug)) {
      boardSlugs.addAll(
          org.getCurriculum().getBoards().stream().map(CurriculumBoard::getSlug).toList());
    } else {
      boardSlugs.add(boardSlug);
    }
    return buildResponse(
        taskRepository.findAllByGradeSlugAndBoardSlugInAndOrgSlugAndTaskType(
            gradeSlug, boardSlugs, orgSlug, TaskType.ELP));
  }

  public List<ElpDto.Chapter> buildResponse(List<Task> tasks) {
    List<ElpDto.Chapter> chaptersResponse = new ArrayList<>();
    if (tasks.isEmpty()) {
      return Collections.emptyList();
    }
    var chapterSlugs = tasks.stream().map(Task::getChapterSlug).distinct().toList();
    var taskMap = tasks.stream().collect(Collectors.groupingBy(Task::getChapterSlug));
    chapterSlugs.forEach(
        chapterSlug -> {
          var filterTasks = taskMap.get(chapterSlug);
          var task = filterTasks.getFirst();
          var testDefinition =
              testDefinitionRepository
                  .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                      task.getElpSlug(), WEXL_INTERNAL);
          chaptersResponse.add(
              ElpDto.Chapter.builder()
                  .gradeSlug(task.getGradeSlug())
                  .chapterName(task.getChapterName())
                  .chapterSlug(task.getChapterSlug())
                  .testDefinitionId(testDefinition.isEmpty() ? null : testDefinition.get().getId())
                  .topics(buildTopics(filterTasks, testDefinition))
                  .build());
        });
    return chaptersResponse.stream()
        .distinct()
        .sorted(Comparator.comparing(ElpDto.Chapter::chapterName))
        .toList();
  }

  private List<ElpDto.Topic> buildTopics(
      List<Task> filterTasks, Optional<TestDefinition> testDefinition) {
    List<ElpDto.Topic> topicResponse = new ArrayList<>();
    filterTasks.forEach(
        task -> {
          if (!"writing".equalsIgnoreCase(task.getSubtopicName().trim())) {
            topicResponse.add(
                ElpDto.Topic.builder()
                    .topicName(task.getSubtopicName())
                    .taskId(task.getId())
                    .topicSlug(task.getSubtopicSlug())
                    .dueDate(getDueDate(testDefinition, task))
                    .build());
          }
        });
    topicResponse.sort(Comparator.comparing(ElpDto.Topic::topicSlug));
    return topicResponse;
  }

  private Long getDueDate(Optional<TestDefinition> testDefinition, Task task) {
    if (testDefinition.isEmpty() || task.getDueDate() == null) {
      return null;
    }
    return DateTimeUtil.convertIso8601ToEpoch(task.getDueDate());
  }

  private Double calculateAttendancePercentage(Task task) {
    var data = buildNotAttemptedCountAndAverage(task);
    var notAttemptedCount = data.get(NOT_ATTEMPT_COUNT);
    var totalCount = task.getTaskInsts().size();
    var attemptedCount = totalCount - ((Long) notAttemptedCount).doubleValue();
    return (double) Math.round((attemptedCount / totalCount) * 100);
  }

  public void triggerElp(String orgSlug, String gradeSlug, List<Long> taskId) {
    final var gradesMapBySlug =
        strapiService.transformGradeEntityToMapBySlug(strapiService.getAllGrades());
    var grade = gradesMapBySlug.get(gradeSlug).getId();
    List<Student> students =
        new ArrayList<>(studentRepository.findStudentsByClassAndOrg(grade, orgSlug));
    if (students.isEmpty()) {
      return;
    }
    taskInstRepository.saveAll(taskInstService.buildTaskInsts(taskId, students, false));
  }

  public List<ElpDto.Chapter> getStudentElps(String studentAuthId) {
    var user = validationUtils.isValidUser(studentAuthId);
    List<ElpDto.Chapter> response = new ArrayList<>();
    var studentTaskInsts = taskInstRepository.findAllByStudentId(user.getStudentInfo().getId());
    var filterElpTasks =
        studentTaskInsts.stream()
            .filter(x -> x.getTask().getTaskType().equals(TaskType.ELP))
            .toList();
    var tasks = filterElpTasks.stream().map(TaskInst::getTask).toList();
    var chapterSlugs = tasks.stream().map(Task::getChapterSlug).distinct().toList();

    Map<Long, TestDefinition> testDefinitionMap = new HashMap<>();
    tasks.forEach(
        task -> {
          var testDefinition =
              testDefinitionRepository
                  .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                      task.getElpSlug(), WEXL_INTERNAL)
                  .orElse(null);
          testDefinitionMap.put(task.getId(), testDefinition);
        });

    chapterSlugs.forEach(
        chapterSlug -> {
          var taskInst =
              filterElpTasks.stream()
                  .filter(x -> x.getTask().getChapterSlug().equals(chapterSlug))
                  .toList();
          var task = taskInst.get(0).getTask();
          var testDefinition = testDefinitionMap.get(task.getId());
          response.add(
              ElpDto.Chapter.builder()
                  .gradeSlug(task.getGradeSlug())
                  .chapterName(task.getChapterName())
                  .chapterSlug(task.getChapterSlug())
                  .testDefinitionId(Objects.nonNull(testDefinition) ? testDefinition.getId() : null)
                  .StudentTaskInst(buildStudentTopics(taskInst))
                  .build());
        });
    response.sort(Comparator.comparing(ElpDto.Chapter::chapterName));
    return response;
  }

  private List<ElpDto.StudentTaskInst> buildStudentTopics(List<TaskInst> taskInsts) {
    List<ElpDto.StudentTaskInst> topicResponse = new ArrayList<>();
    taskInsts.forEach(
        inst -> {
          var exam = inst.getExam();
          var task = inst.getTask();
          if (!"writing".equalsIgnoreCase(task.getSubtopicName().trim())) {
            topicResponse.add(
                ElpDto.StudentTaskInst.builder()
                    .topicName(task.getSubtopicName())
                    .taskId(task.getId())
                    .topicSlug(task.getSubtopicSlug())
                    .taskInstId(inst.getId())
                    .status(getExamStatus(exam))
                    .examId(exam == null ? null : exam.getId())
                    .dueDate(
                        task.getDueDate() == null
                            ? null
                            : DateTimeUtil.convertIso8601ToEpoch(task.getDueDate()))
                    .remarks(inst.getRemarks())
                    .build());
          }
        });
    topicResponse.sort(Comparator.comparing(ElpDto.StudentTaskInst::topicSlug));
    return topicResponse;
  }

  private TaskStatus getExamStatus(Exam exam) {
    if (exam == null) {
      return TaskStatus.NOT_STARTED;
    }
    return exam.getExamAnswers().isEmpty() ? TaskStatus.NOT_STARTED : TaskStatus.COMPLETED;
  }

  public ExamResponse startElpExam(long tasKInstId) {
    var taskInst = validationUtils.getTaskInstById(tasKInstId);
    var task = taskInst.getTask();
    Exam exam = examFactory.createElpExam(task.getId());
    exam.setAllowedDuration(Constants.ALLLOWED_DURATION);
    exam.setTaskId(task.getId());
    exam.setChapterId(task.getChapterId());
    exam.setSubTopicId(task.getSubtopicId());
    exam.setSubtopicName(task.getName());
    exam.setSubtopicSlug(task.getSubtopicSlug());
    exam.setChapterName(task.getChapterName());
    exam.setChapterSlug(task.getChapterSlug());
    exam.setSubjectSlug(task.getSubjectSlug());
    exam.setSubjectName(task.getSubjectName());
    exam.setTaskInstId(taskInst.getId());
    return examTransformer.mapExamToExamResponse(examRepository.save(exam));
  }

  public QuestionDto.QuestionResponse getExamQuestionResponse(long examId) {
    var exam = validationUtils.findByExamId(examId);
    var task = validationUtils.getTaskById(exam.getTaskId());
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                task.getElpSlug(), WEXL_INTERNAL)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, NOT_FOUND));
    return testDefinitionService.getTestDefinitionQuestions(testDefinition.getId(), 1);
  }

  public void submitElpTest(ElpDto.StudentAnswerRequest studentAnswerRequest) {
    submitExam(studentAnswerRequest, WEXL_INTERNAL);
  }

  public void submitExam(ElpDto.StudentAnswerRequest studentAnswerRequest, String orgSlug) {
    var exam = validationUtils.findByExamId(studentAnswerRequest.examId());

    if (exam.getExamAnswers().isEmpty() && exam.getEndTime() == null) {
      var examAnswers = buildExamAnswersEntity(studentAnswerRequest, exam, orgSlug);
      exam.setCompleted(true);
      exam.setExamAnswers(examAnswers);
      exam.setTotalMarks(
          (float) examAnswers.stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum());
      exam.setMarksScored(calculateMarkScored(examAnswers));
      exam.setEndTime(Timestamp.from(Instant.now()));
      examRepository.save(exam);
      var taskInst = validationUtils.getTaskInstById(exam.getTaskInstId());
      taskInst.setCompletedAt(LocalDateTime.now());
      taskInst.setCompletionStatus(TaskStatus.COMPLETED);
      taskInst.setExam(exam);
      taskInstRepository.save(taskInst);
    }
  }

  public Float calculateMarkScored(List<ExamAnswer> examMarks) {
    var marks = (float) examMarks.stream().mapToDouble(ExamAnswer::getMarksScoredPerQuestion).sum();
    return Float.parseFloat(Constants.DECIMAL_FORMAT.format(marks));
  }

  public List<ExamAnswer> buildExamAnswersEntity(
      ElpDto.StudentAnswerRequest studentAnswerRequest, Exam exam, String orgSlug) {
    List<ExamAnswer> examAnswersList = new ArrayList<>();

    studentAnswerRequest
        .questions()
        .forEach(
            answer -> {
              ExamAnswer examAnswer = new ExamAnswer();
              var testQuestion =
                  contentService.getQuestionsByUuid(
                      contentBearerToken,
                      answer.questionType().getType().toUpperCase(),
                      answer.questionUuid(),
                      orgSlug);
              if (testQuestion.questions().isEmpty()) {
                throw new ApiException(
                    InternalErrorCodes.NO_RECORD_FOUND,
                    "error.Invalid.Elp.Question.Org",
                    new String[] {answer.questionUuid()});
              }
              boolean isSubjective =
                  answer
                      .questionType()
                      .toString()
                      .equalsIgnoreCase(QuestionType.SUBJECTIVE.toString());

              var results = examUtils.calculateMarks(testQuestion.questions().getFirst(), answer);
              examAnswer.setSubtopicSlug(testQuestion.questions().getFirst().subtopicSlug());
              examAnswer.setQuestionUuid(answer.questionUuid());
              examAnswer.setType(answer.questionType().getType());
              examAnswer.setExam(exam);
              examAnswer.setExamReference(exam.getId());
              examAnswer.setIsMobile(authService.isUserLoginByMobile());
              examAnswer.setSelectedOption(answer.mcqSelectedAnswer());
              examAnswer.setFbqSelectedAnswer(answer.fbqSelectedAnswer());
              examAnswer.setAmcqSelectedAnswer(answer.amcqSelectedAnswer());
              examAnswer.setSpchSelectedAnswer(answer.spchSelectedAnswer());
              examAnswer.setMsqSelectedAnswer(answer.msqSelectedAnswer());
              examAnswer.setYesNoSelectedAnswer(answer.yesNoSelectedAnswer());
              examAnswer.setNatSelectedAnswer(answer.natSelectedAnswer());
              examAnswer.setPbqAnswers(answer.pbqSelectedAnswer());
              examAnswer.setSubjectiveWrittenAnswer(answer.subjectiveWrittenAnswer());
              examAnswer.setActive(true);
              examAnswer.setAttempted(StudentTestAttemptStatus.ANSWERED.equals(answer.status()));
              examAnswer.setCorrect(!isSubjective && results.get(MARKS_SCORED) > 0);
              examAnswer.setMarksScoredPerQuestion(isSubjective ? 0.0f : results.get(MARKS_SCORED));
              examAnswer.setMarksPerQuestion(
                  ELP_EXAM == exam.getExamType() && Objects.isNull(answer.testQuestionMarks())
                      ? testQuestion.questions().getFirst().marks()
                      : answer.testQuestionMarks());
              examAnswer.setPbqAnswers(
                  getPbqResponse(
                      testQuestion.questions().getFirst().pbq(), answer.pbqSelectedAnswer()));
              examAnswer.setAiAnalysis(answer.aiAnalysis());
              examAnswer.setAiMarks(answer.aiMarks());
              examAnswersList.add(examAnswer);
            });
    return examAnswersList;
  }

  private PbqDto.Data getPbqResponse(
      List<QuestionDto.Pbq> testQuestion, PbqDto.Data studentAnswers) {
    if (testQuestion == null || studentAnswers == null) {
      return null;
    }
    var pbqAnswers = studentAnswers.answers();
    List<PbqDto.Answers> pbqAnswerResponses = new ArrayList<>();

    for (QuestionDto.Pbq testAnswer : testQuestion) {
      for (PbqDto.Answers selectedAnswer : pbqAnswers) {
        pbqAnswerResponses.add(
            PbqDto.Answers.builder()
                .mcq(
                    PbqDto.Mcq.builder()
                        .questionUuid(selectedAnswer.mcq().questionUuid())
                        .answer(
                            selectedAnswer.mcq().questionUuid().equals(testAnswer.uuid())
                                ? testAnswer.mcq().answer().intValue()
                                : null)
                        .selectedAnswer(selectedAnswer.mcq().selectedAnswer())
                        .build())
                .build());
      }
    }
    return PbqDto.Data.builder().answers(pbqAnswerResponses).build();
  }

  public ElpDto.TaskResponse getTaskDetails(List<Long> taskIds) {
    var taskInst = taskInstRepository.getTaskInstByTaskId(taskIds);
    if (taskInst.isEmpty()) {
      return null;
    }
    return ElpDto.TaskResponse.builder()
        .summary(buildSummary(taskIds))
        .data(buildData(taskInst))
        .build();
  }

  private List<ElpDto.Data> buildData(List<TaskInst> taskInst) {
    var students = taskInst.stream().map(TaskInst::getStudent).distinct().toList();
    return students.stream()
        .filter(student -> Objects.nonNull(student.getUserInfo()))
        .map(
            student -> {
              var user = student.getUserInfo();
              var studentTasks = buildStudentTopic(taskInst, student);
              return ElpDto.Data.builder()
                  .name(userService.getNameByUserInfo(user))
                  .sectionName(student.getSection().getName())
                  .userId(user.getAuthUserId())
                  .status(getStudentStatus(studentTasks))
                  .studentTaskInsts(studentTasks)
                  .build();
            })
        .toList();
  }

  private TaskStatus getStudentStatus(List<ElpDto.StudentTaskInst> studentTaskInsts) {
    var filterTaskStatus =
        studentTaskInsts.stream().map(ElpDto.StudentTaskInst::status).distinct().toList();
    if (filterTaskStatus.contains(TaskStatus.IN_PROGRESS)) {
      return TaskStatus.IN_PROGRESS;
    } else if (filterTaskStatus.contains(TaskStatus.COMPLETED)) {
      return TaskStatus.COMPLETED;
    } else {
      return TaskStatus.NOT_STARTED;
    }
  }

  private List<ElpDto.StudentTaskInst> buildStudentTopic(
      List<TaskInst> taskInsts, Student student) {
    List<ElpDto.StudentTaskInst> topic = new ArrayList<>();
    var studentTaskInst = taskInsts.stream().filter(x -> x.getStudent().equals(student)).toList();
    if (studentTaskInst.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "invalid TaskInst");
    }
    studentTaskInst.forEach(
        inst -> {
          var exam = inst.getExam();
          var task = inst.getTask();

          boolean isSpeaking = StringUtils.equalsIgnoreCase("speaking", task.getSubtopicName());

          double taskKm = 0.0;
          if (isSpeaking) {

            Double speakingMarks = buildSpeakingMarks(inst);
            taskKm =
                exam == null
                    ? 0.0
                    : calculatePercentage(
                        Float.parseFloat(String.format("%.1f", speakingMarks)),
                        exam.getTotalMarks());

            taskKm = Double.parseDouble(String.format("%.1f", taskKm));
          } else {

            taskKm = exam == null ? 0.0 : calculateKnowledgePercentage(exam);
          }
          topic.add(
              ElpDto.StudentTaskInst.builder()
                  .topicName(inst.getTask().getSubtopicName())
                  .taskId(inst.getTask().getId())
                  .topicSlug(inst.getTask().getSubtopicSlug())
                  .taskInstId(inst.getId())
                  .status(inst.getCompletionStatus())
                  .examId(exam == null ? null : exam.getId())
                  .taskKm(taskKm)
                  .markScored(Objects.nonNull(exam) ? exam.getMarksScored() : null)
                  .attemptedCount(
                      Objects.nonNull(exam) && Objects.nonNull(exam.getExamAnswers())
                          ? exam.getExamAnswers().size()
                          : null)
                  .remarks(inst.getRemarks())
                  .build());
        });

    topic.sort(Comparator.comparing(StudentTaskInst::topicName));
    return topic;
  }

  private Double calculateKnowledgePercentage(Exam exam) {
    if (exam.getMarksScored() == null || exam.getTotalMarks() == null) {
      return 0.0;
    }
    return Objects.requireNonNullElse(
        (double)
            Math.round(
                exam.getMarksScored().doubleValue() / exam.getTotalMarks().doubleValue() * 100),
        0D);
  }

  private ElpDto.Summary buildSummary(List<Long> taskIds) {
    var taskList = taskRepository.findAllById(taskIds);
    var task = taskList.getFirst();
    var user = task.getTeacher().getUserInfo();
    return ElpDto.Summary.builder()
        .chapterName(task.getChapterName())
        .chapterSlug(task.getChapterSlug())
        .subjectName(task.getSubjectName())
        .date(
            task.getDueDate() == null
                ? null
                : DateTimeUtil.convertIso8601ToEpoch(task.getDueDate()))
        .gradeName(task.getGradeName())
        .gradeSlug(task.getGradeSlug())
        .orgName(task.getOrgSlug())
        .orgSlug(task.getOrgSlug())
        .topicSummary(buildTopicSummary(taskList))
        .teacherName(user.getFirstName() + " " + user.getLastName())
        .build();
  }

  private List<ElpDto.TopicSummary> buildTopicSummary(List<Task> tasks) {
    List<ElpDto.TopicSummary> topicSummaries = new ArrayList<>();
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                tasks.getFirst().getElpSlug(), WEXL_INTERNAL)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, NOT_FOUND));
    var questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);
    tasks.forEach(
        task -> {
          Map<String, Object> data;
          data = buildNotAttemptedCountAndAverage(task);
          topicSummaries.add(
              ElpDto.TopicSummary.builder()
                  .name(task.getSubtopicName())
                  .questionCount(buildQuestionCount(questionResponse, task.getSubtopicName()))
                  .average((Double) data.get("average"))
                  .notAttemptedCount((Long) data.get(NOT_ATTEMPT_COUNT))
                  .build());
        });
    topicSummaries.sort(Comparator.comparing(ElpDto.TopicSummary::name));
    return topicSummaries;
  }

  private Map<String, Object> buildNotAttemptedCountAndAverage(Task task) {
    Map<String, Object> data = new HashMap<>();
    AtomicLong notAttemptedCount = new AtomicLong();
    AtomicLong completedExamsCount = new AtomicLong();
    AtomicReference<Double> totalTaskKnowledgeMeter = new AtomicReference<>(0.0);

    boolean isSpeakingTask = StringUtils.equalsIgnoreCase("speaking", task.getSubtopicName());

    task.getTaskInsts()
        .forEach(
            inst -> {
              var exam = inst.getExam();
              if (exam == null
                  || exam.getExamAnswers() == null
                  || exam.getExamAnswers().isEmpty()) {
                notAttemptedCount.getAndIncrement();
              } else {
                completedExamsCount.getAndIncrement();
                if (isSpeakingTask) {
                  Double speakingMarks = buildSpeakingMarks(inst);
                  if (speakingMarks > 0) {
                    float taskKmPercentage =
                        calculatePercentage(
                            Float.parseFloat(String.format("%.1f", speakingMarks)),
                            exam.getTotalMarks());
                    totalTaskKnowledgeMeter.updateAndGet(v -> v + taskKmPercentage);
                  }
                } else {
                  double taskKmPercentage = calculateKnowledgePercentage(exam);
                  totalTaskKnowledgeMeter.updateAndGet(v -> v + taskKmPercentage);
                }
              }
            });

    double average = 0.0;
    if (completedExamsCount.get() > 0) {
      average = totalTaskKnowledgeMeter.get() / completedExamsCount.get();
      average = Math.round(average * 10.0) / 10.0;
    }

    data.put(NOT_ATTEMPT_COUNT, notAttemptedCount.get());
    data.put("average", average);

    return data;
  }

  private Long buildQuestionCount(
      QuestionDto.QuestionResponse questionResponse, String subtopicName) {
    var questions =
        questionResponse.testDefinitionSectionResponses().stream()
            .filter(x -> x.name().equals(subtopicName))
            .findFirst();
    return questions.isEmpty() ? 0L : questions.get().noOfQuestions();
  }

  public QuestionDto.StudentResultsResponse getExamResult(long examId) {
    Exam exam = validationUtils.findByExamId(examId);
    var taskInst = validationUtils.getTaskInstById(exam.getTaskInstId());
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                taskInst.getTask().getElpSlug(), WEXL_INTERNAL)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, NOT_FOUND));

    return buildExamResults(exam, testDefinition);
  }

  public QuestionDto.StudentResultsResponse buildExamResults(
      Exam exam, TestDefinition testDefinition) {

    Double speakingMarks = 0.0d;
    boolean isSpeaking = false;

    var taskInst = validationUtils.getTaskInstById(exam.getTaskInstId());

    var task = validationUtils.getTaskById(taskInst.getTask().getId());

    if (StringUtils.equalsIgnoreCase("speaking", task.getSubtopicName())) {
      speakingMarks = buildSpeakingMarks(taskInst);
      isSpeaking = true;
    }

    double totalMarksSecured =
        Double.parseDouble(
            String.format(
                "%.1f",
                isSpeaking
                    ? speakingMarks
                    : (exam.getMarksScored() == null || exam.getMarksScored() < 0)
                        ? 0.0
                        : exam.getMarksScored()));

    var questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);
    return QuestionDto.StudentResultsResponse.builder()
        .examId(exam.getId())
        .testDefinitionId(testDefinition.getId())
        .testName(testDefinition.getTestName())
        .gradeName(testDefinition.getGradeSlug())
        .noOfQuestions(testDefinition.getNoOfQuestions().longValue())
        .testDefinitionSection(
            scheduleTestStudentService.buildTestDefinitionSectionResult(
                questionResponse, exam, testDefinition))
        .totalMarks(exam.getTotalMarks())
        .totalMarksSecured((float) totalMarksSecured)
        .totalMarks(exam.getTotalMarks())
        .percentageSecured(calculatePercentage((float) totalMarksSecured, exam.getTotalMarks()))
        .assetSlug(
            Objects.nonNull(testDefinition.getMetadata().getAssetSlug())
                ? testDefinition.getMetadata().getAssetSlug()
                : null)
        .build();
  }

  public Float calculatePercentage(Float marksScored, Float totalMarks) {
    if (marksScored == null) {
      return 0F;
    }
    return (marksScored / totalMarks * 100) < 0 ? 0.0f : Math.round(marksScored / totalMarks * 100);
  }

  public void addRemarks(long taskInstId, ElpDto.Remarks request) {
    var taskInst = validationUtils.getTaskInstById(taskInstId);
    taskInst.setRemarks(request.remarks());
    taskInstRepository.save(taskInst);
  }

  public List<GenericMetricResponse> getLeaderBoardResults(
      List<Long> taskIds, List<String> sections) {
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    List<TaskInst> taskInsts;
    if (Objects.isNull(sections) || sections.isEmpty()) {
      taskInsts = taskInstRepository.getTaskInstByTaskId(taskIds);
    } else {
      List<UUID> sectionUuids = new ArrayList<>();
      sections.forEach(sec -> sectionUuids.add(UUID.fromString(sec)));
      taskInsts = taskInstRepository.getTaskInstByTaskIdAndSections(taskIds, sectionUuids);
    }
    if (taskInsts.isEmpty()) {
      return Collections.emptyList();
    }
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
                taskInsts.getFirst().getTask().getElpSlug(), WEXL_INTERNAL)
            .orElse(new TestDefinition());

    Map<Student, List<TaskInst>> studentInstMap =
        taskInsts.stream().collect(Collectors.groupingBy(TaskInst::getStudent));

    Integer totalMarks =
        testDefinition.getTestDefinitionSections().stream()
            .map(TestDefinitionSection::getTestQuestions)
            .flatMap(Collection::stream)
            .mapToInt(TestQuestion::getMarks)
            .sum();

    studentInstMap.forEach(
        (student, taskInstList) -> {
          Status status =
              studentAuthTransformer.isDisconnected(student.getUserInfo().getDeletedAt());
          if (status == Status.ACTIVE) {
            genericMetricResponses.add(
                GenericMetricResponse.builder()
                    .summary(buildLeaderBoardSummary(student, testDefinition))
                    .data(buildLeaderBoardResult(taskInstList, totalMarks))
                    .build());
          }
        });
    return getSortedResponse(genericMetricResponses);
  }

  private List<GenericMetricResponse> getSortedResponse(
      List<GenericMetricResponse> genericMetricResponses) {

    return genericMetricResponses.stream()
        .sorted(Comparator.comparing(response -> (Double) response.getData().get("secured_marks")))
        .collect(
            Collectors.collectingAndThen(
                Collectors.toList(),
                list -> {
                  Collections.reverse(list);
                  return list;
                }));
  }

  private Map<String, Object> buildLeaderBoardSummary(
      Student student, TestDefinition testDefinition) {
    Map<String, Object> resultMap = new HashMap<>();

    resultMap.put("school_name", student.getSchoolName());
    resultMap.put("user_name", student.getUserInfo().getUserName());
    resultMap.put("student_name", userService.getNameByUserInfo(student.getUserInfo()));
    resultMap.put("test_name", testDefinition.getTestName());
    resultMap.put("section_name", student.getSection().getName());
    return resultMap;
  }

  private Map<String, Object> buildLeaderBoardResult(List<TaskInst> taskInsts, Integer totalMarks) {

    Map<String, Object> resultMap = new HashMap<>();

    taskInsts.forEach(
        inst -> {
          if (StringUtils.equalsIgnoreCase("speaking", inst.getTask().getSubtopicName())) {
            resultMap.put(inst.getTask().getSubtopicName(), buildSpeakingMarks(inst));
          } else {
            resultMap.put(
                inst.getTask().getSubtopicName(),
                Objects.nonNull(inst.getExam()) ? inst.getExam().getMarksScored() : null);
          }
        });

    double marks =
        resultMap.values().stream()
            .filter(Number.class::isInstance)
            .mapToDouble(value -> ((Number) value).doubleValue())
            .sum();

    resultMap.put("secured_marks", marks == 0.0 ? 0.0 : marks);
    resultMap.put("total_marks", totalMarks);
    return resultMap;
  }

  private Double buildSpeakingMarks(TaskInst inst) {
    try {
      if (Objects.isNull(inst.getExam())) {
        return 0.0d;
      }
      var speechResponses =
          inst.getExam().getExamAnswers().stream()
              .map(speechEvaluationService::evaluateSpeakingTestByExamAnswer)
              .filter(Objects::nonNull)
              .mapToDouble(speechResponse -> (speechResponse.assessment().ieltsScore() / 9) * 1)
              .sum();
      return (double) ((int) (speechResponses * 10)) / 10.0;
    } catch (Exception e) {
      return 0.0d;
    }
  }

  public TestDefinition configElp(ElpDto.ConfigElp request, String orgSlug) {
    return testDefinitionRepository.save(buildTestDefinition(orgSlug, request));
  }

  private TestDefinition buildTestDefinition(String orgSlug, ElpDto.ConfigElp request) {
    TestDefinition testDefinition = new TestDefinition();
    testDefinition.setTestName(buildTestName(request.boardSlug(), request.chapterSlug()));
    testDefinition.setCategory(TestCategory.ELP);
    testDefinition.setOrganization(orgSlug);
    testDefinition.setType(request.testType());
    testDefinition.setTeacher(authService.getTeacherDetails());
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    testDefinition.setTestDefinitionSections(
        buildTestDefinitionSections(testDefinition, request, orgSlug));
    testDefinition.setActive(Boolean.TRUE);
    testDefinition.setGradeSlug(request.gradeSlug());
    testDefinition.setBoardSlug(request.boardSlug());
    var questionsCount =
        testDefinition.getTestDefinitionSections().stream()
            .map(TestDefinitionSection::getTestQuestions)
            .mapToLong(List::size)
            .sum();
    testDefinition.setNoOfQuestions((int) questionsCount);
    return testDefinition;
  }

  private List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, ElpDto.ConfigElp request, String orgSlug) {
    List<TestDefinitionSection> sectionList = new ArrayList<>();
    QuestionDto.SearchQuestionResponse contentQuestions;

    contentQuestions = getQuestionsData(buildElpQuestionsRequest(request), orgSlug);
    for (int i = 0; i < sectionNames.length; i++) {
      TestDefinitionSection tds = new TestDefinitionSection();
      var questions =
          buildElpTestQuestions(contentQuestions, sectionNames[i], request.chapterSlug(), tds);
      tds.setName((sectionNames[i]));
      tds.setNoOfQuestions((long) questions.size());
      tds.setSequenceNumber(i + 1L);
      tds.setTestDefinition(testDefinition);
      tds.setTestQuestions(questions);
      sectionList.add(tds);
    }

    return sectionList;
  }

  private ElpDto.Request buildElpQuestionsRequest(ElpDto.ConfigElp request) {
    return ElpDto.Request.builder()
        .gradeName(request.gradeName())
        .gradeSlug(request.gradeSlug())
        .chapterSlug(request.chapterSlug())
        .chapterName(request.chapterName())
        .subjectSlug(request.subjectSlug())
        .subjectName(request.subjectName())
        .boardSlug(request.boardSlug())
        .boardName(request.boardName())
        .build();
  }

  private List<TestQuestion> buildElpTestQuestions(
      QuestionDto.SearchQuestionResponse contentQuestions,
      String sectionName,
      String chapterSlug,
      TestDefinitionSection tds) {
    var subtopic = buildSubtopicSlug(sectionName.toLowerCase(), chapterSlug);
    var questions =
        contentQuestions.questions().stream()
            .filter(x -> x.subtopicSlug().equals(subtopic))
            .toList();

    return buildTestQuestions(questions, tds);
  }

  private String buildTestName(String boardSlug, String chapterSlug) {
    return "%s-%s".formatted(boardSlug, chapterSlug);
  }

  public void switchElpStudent(ElpDto.SwitchElpStudentRequest transferRequest, String orgSlug) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    if (Objects.isNull(org) || !Boolean.TRUE.equals(org.getSelfSignup())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound");
    }
    User user = validateUser(transferRequest, orgSlug);
    var authUserId = user.getAuthUserId();
    var student = studentAuthService.validateStudentByUser(user);
    try {
      deleteExistingStudent(user, student);
      createNewStudent(transferRequest, user);
    } catch (Exception e) {
      log.error("Error occurred while  transfer student : " + e.getMessage());
      revertBack(user, student, authUserId);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private void revertBack(User user, Student student, String authUserId) {
    user.setDeletedAt(null);
    user.setIsDeleted(null);
    user.setAuthUserId(authUserId);
    student.setDeletedAt(null);
    userRepository.save(user);
    studentRepository.save(student);
  }

  private User validateUser(ElpDto.SwitchElpStudentRequest transferRequest, String orgSlug) {
    var user =
        userRepository
            .findByUserNameAndOrganizationAndDeletedAtIsNull(transferRequest.userName(), orgSlug)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    if (!passwordEncoder.matches(transferRequest.password(), user.getPassword())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidPassword");
    }
    return user;
  }

  private void deleteExistingStudent(User user, Student student) {
    user.setDeletedAt(Timestamp.valueOf(LocalDateTime.now()));
    user.setIsDeleted(Boolean.TRUE);
    user.setAuthUserId(
        String.format(
            "mig-%s-%s",
            user.getUserName(),
            DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC))));
    userRepository.save(user);
    studentRepository.save(student);
  }

  private void createNewStudent(ElpDto.SwitchElpStudentRequest transferRequest, User user) {
    var organization =
        organizationRepository
            .findByStudentPasscode(transferRequest.passcode())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidPasscode"));
    studentAuthService.createStudent(
        buildStudentRequest(transferRequest, user, organization.getSlug()), organization.getSlug());
  }

  private StudentRequest buildStudentRequest(
      ElpDto.SwitchElpStudentRequest transferRequest, User user, String orgSlug) {

    var student = user.getStudentInfo();
    var studentSection = student.getSection();
    EduBoard eduBoard = curriculumService.getBoardByGrade(orgSlug, studentSection.getGradeSlug());
    var sections = sectionService.getSectionsByGradeIdAndOrg(student.getClassId(), orgSlug, true);
    return StudentRequest.builder()
        .academicYearSlug(latestAcademicYear)
        .userName(transferRequest.userName())
        .firstName(user.getFirstName())
        .lastName(user.getLastName())
        .gender(user.getGender())
        .attributes(student.getAttributes())
        .boardSlug(eduBoard.getSlug())
        .gradeSlug(studentSection.getGradeSlug())
        .mobileNumber(user.getMobileNumber())
        .schoolName(student.getSchoolName())
        .section(sections.get(0).getName())
        .rollNumber(student.getRollNumber())
        .orgSlug(orgSlug)
        .password(transferRequest.password())
        .build();
  }

  public List<GenericMetricResponse> getElpCountsByGrade(String orgSlug, Long frmDate) {
    List<GenericMetricResponse> responseList = new ArrayList<>();

    var countsByGrades =
        taskRepository.getTasksCountByOrgAndType(
            orgSlug, TaskType.ELP.name(), dateTimeUtil.repositoryFriendlyDateFromEpoch(frmDate));

    Map<String, Object> data = new HashMap<>();
    data.put("counts", buildElpCountsByGradeResponse(countsByGrades, orgSlug));

    responseList.add(GenericMetricResponse.builder().data(data).build());

    return responseList;
  }

  private List<Map<String, Object>> buildElpCountsByGradeResponse(
      List<ElpCountsByGrade> countsByGrades, String orgSlug) {
    List<Map<String, Object>> data = new ArrayList<>();

    if (countsByGrades.isEmpty()) {
      return data;
    }

    countsByGrades.forEach(
        grade -> {
          Map<String, Object> gradeData = new HashMap<>();
          List<String> gradeSlug = Collections.singletonList(grade.getGradeSlug());
          List<Student> students = studentRepository.findByGradeAndOrg(gradeSlug, orgSlug);
          gradeData.put("grade_name", grade.getGradeName());
          gradeData.put("elp_counts", grade.getCount());
          gradeData.put("total_students", students.size());
          gradeData.put("students_attemted", grade.getStudentsAttemted());
          gradeData.put(
              "student_attemted_percentage", calculateStudentAttendancePercentage(grade, students));
          data.add(gradeData);
        });

    return data;
  }

  public List<GenericMetricResponse> getElpCountsByStudent(
      String orgSlug, Long frmDate, List<String> section) {
    List<GenericMetricResponse> sectionList = new ArrayList<>();
    if (section == null || section.isEmpty()) {
      List<SectionEntityDto.Response> allSections = sectionService.getAllSectionsByOrg(orgSlug);
      allSections.forEach(
          sec -> {
            String sectionUuid = String.valueOf(sec.uuid());
            sectionList.addAll(fetchElpCountsForSection(orgSlug, frmDate, sectionUuid));
          });
    } else {
      section.forEach(sec -> sectionList.addAll(fetchElpCountsForSection(orgSlug, frmDate, sec)));
    }
    return sectionList;
  }

  private List<GenericMetricResponse> fetchElpCountsForSection(
      String orgSlug, Long frmDate, String section) {
    Map<String, Object> data = new HashMap<>();
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var studentCounts =
        taskRepository.getElpCountsByStudent(
            orgSlug,
            TaskType.ELP.name(),
            dateTimeUtil.repositoryFriendlyDateFromEpoch(frmDate),
            UUID.fromString(section));

    data.put("counts", buildElpCountsByStudentResponse(studentCounts));
    responseList.add(GenericMetricResponse.builder().data(data).build());

    return responseList;
  }

  private List<Map<String, Object>> buildElpCountsByStudentResponse(
      List<ElpCountsByStudents> studentCounts) {
    List<Map<String, Object>> data = new ArrayList<>();
    if (studentCounts.isEmpty()) {
      return data;
    }

    studentCounts.forEach(
        grade -> {
          Map<String, Object> studentData = new HashMap<>();
          studentData.put("name", grade.getFullName());
          studentData.put("grade_name", grade.getGradeName());
          studentData.put("assigned_count", grade.getAssignedCount());
          studentData.put("attended_count", grade.getAttendedCount());
          studentData.put("section_name", grade.getSectionName());
          studentData.put(
              "listening_percentage",
              Math.round((grade.getListeningCount() / grade.getAssignedCount()) * 100));
          studentData.put(
              "speaking_percentage",
              Math.round((grade.getSpeakingCount() / grade.getAssignedCount()) * 100));
          studentData.put(
              "reading_percentage",
              Math.round((grade.getReadingCount() / grade.getAssignedCount()) * 100));
          studentData.put(
              "vocabulary_percentage",
              Math.round((grade.getVocabularyCount() / grade.getAssignedCount()) * 100));
          studentData.put(
              "grammar_percentage",
              Math.round((grade.getGrammarCount() / grade.getAssignedCount()) * 100));
          studentData.put("attendance_percentage", calculateStudentAttendancePercentage(grade));
          data.add(studentData);
        });

    return data;
  }

  private double calculateStudentAttendancePercentage(ElpCountsByStudents grade) {
    double assignedCount = grade.getAssignedCount();
    double attendedCount = grade.getAttendedCount();
    double attendancePercentage = assignedCount > 0 ? (attendedCount / assignedCount) * 100 : 0.0;
    return Math.round(attendancePercentage * 100.0) / 100.0;
  }

  private double calculateStudentAttendancePercentage(
      ElpCountsByGrade grade, List<Student> students) {
    double assignedCount = grade.getCount();
    double attendedCount = grade.getStudentsAttemted();
    double attendancePercentage = assignedCount > 0 ? (attendedCount / students.size()) * 100 : 0.0;
    return Math.round(attendancePercentage * 100.0) / 100.0;
  }

  public List<GenericMetricResponse> getElpCountsByTeacher(
      String orgSlug, Long fromDate, List<String> gradeList) {
    Map<String, Object> data = new HashMap<>();
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var teacherCounts =
        taskRepository.getElpCountsTeacher(
            orgSlug,
            TaskType.ELP.name(),
            dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate),
            gradeList);

    data.put("counts", buildElpCountsByTeacherResponse(teacherCounts));
    responseList.add(GenericMetricResponse.builder().data(data).build());

    return responseList;
  }

  private List<Map<String, Object>> buildElpCountsByTeacherResponse(
      List<ElpCountsByTeacher> teacherCounts) {
    List<Map<String, Object>> data = new ArrayList<>();
    if (teacherCounts.isEmpty()) {
      return data;
    }

    teacherCounts.forEach(
        grade -> {
          Map<String, Object> teacherData = new HashMap<>();
          teacherData.put("name", grade.getFullName());
          teacherData.put("assigned_count", grade.getAssignedCount());
          teacherData.put("grade_name", grade.getGradeName());
          teacherData.put("attendance_percentage", calculateTeacherAttendancePercentage(grade));
          data.add(teacherData);
        });

    return data;
  }

  private double calculateTeacherAttendancePercentage(ElpCountsByTeacher grade) {
    double totalStudents = grade.getTotalStudents();
    double totalChapters = grade.getAssignedCount();
    double totalAttendance = totalStudents * totalChapters;
    double actualAttendances = grade.getAttendedCount();
    double attendancePercentage =
        totalAttendance > 0 ? (actualAttendances / totalAttendance) * 100.0 : 0.0;
    return Math.round(attendancePercentage * 100.0) / 100.0;
  }

  public List<GenericMetricResponse> getElpReport(String orgSlug) {
    List<String> orgSlugs =
        Optional.ofNullable(getChildOrgSlugs(orgSlug)).orElse(Collections.emptyList());
    if (orgSlugs.isEmpty()) {
      return Collections.emptyList();
    }

    List<ElpCounts> metrics =
        Optional.ofNullable(taskRepository.getElpCountsByOrg(orgSlugs))
            .orElse(Collections.emptyList());

    return metrics.stream()
        .map(
            metric ->
                GenericMetricResponse.builder()
                    .data(
                        Map.of(
                            "institute_name", metric.getInstituteName(),
                            "org_slug", metric.getOrgSlug(),
                            "count", metric.getCount()))
                    .build())
        .collect(Collectors.toList());
  }

  private List<String> getChildOrgSlugs(String orgSlug) {
    User user = authService.getUserDetails();
    List<String> teacherOrgSlugs =
        teacherOrgsService.getChildOrgs(user.getAuthUserId()).stream()
            .map(Organization::getSlug)
            .toList();
    List<String> childOrgSlugs = organizationRepository.getAllChildOrgSlugs(orgSlug);
    return teacherOrgSlugs.stream().filter(childOrgSlugs::contains).toList();
  }

  public SpeechResponse evaluateSpeakingTest(long examId, String questionUuid) {
    return speechEvaluationService.evaluateSpeakingTest(examId, questionUuid);
  }

  private String buildSubtopicSlug(String section, String chapterSlug) {
    return chapterSlug + "-" + section.toLowerCase();
  }

  private QuestionDto.SearchQuestionResponse getQuestionsData(
      ElpDto.Request request, String orgSlug) {
    return contentService.getElpQuestions(request, orgSlug);
  }

  private List<TestQuestion> buildTestQuestions(
      List<QuestionDto.Question> questions, TestDefinitionSection tds) {

    return questions.stream()
        .map(
            question -> {
              TestQuestion.TestQuestionBuilder builder =
                  TestQuestion.builder()
                      .marks(Math.toIntExact(question.marks()))
                      .questionUuid(question.uuid())
                      .type(question.type().name())
                      .testDefinitionSection(tds)
                      .chapterSlug(question.chapterSlug())
                      .chapterName(question.chapterSlug())
                      .complexity(question.complexity())
                      .subtopicSlug(question.subtopicSlug())
                      .subjectSlug(question.subjectSlug())
                      .category(question.category());
              setConditionalAnswers(builder, question);

              return builder.build();
            })
        .toList();
  }

  private void setConditionalAnswers(
      TestQuestion.TestQuestionBuilder builder, QuestionDto.Question question) {
    QuestionType type = QuestionType.valueOf(question.type().name());
    if (type == QuestionType.MCQ) {
      builder.mcqAnswer(question.mcq().answer());
    } else if (type == QuestionType.AMCQ) {
      builder.amcqAnswer(question.amcq().answer());
    } else if (type == QuestionType.SPCH) {
      builder.spchAnswer(question.spch().answerAudioPath());
    } else if (type == QuestionType.FBQ) {
      builder.fbqAnswer(question.fbq().answer());
    } else if (type == QuestionType.PBQ) {
      builder.pbqAnswers(buildPbqAnswers(question.pbq()));
    }
  }

  private PbqDto.Data buildPbqAnswers(List<QuestionDto.Pbq> pbqs) {
    List<PbqDto.Answers> answers = new ArrayList<>();
    pbqs.forEach(
        pbq ->
            answers.add(
                PbqDto.Answers.builder()
                    .mcq(PbqDto.Mcq.builder().questionUuid(pbq.uuid()).answer(pbq.marks()).build())
                    .build()));
    return PbqDto.Data.builder().answers(answers).build();
  }

  public DashBoardResponse.DashBoardDetails getStudentElpDashboard(
      String orgSlug, String studentAuthId) {
    var user = validationUtils.isValidUser(studentAuthId);
    var studentTaskInsts = taskInstRepository.findAllByStudentId(user.getStudentInfo().getId());
    var filterElpTasks =
        studentTaskInsts.stream()
            .filter(x -> x.getTask().getTaskType().equals(TaskType.ELP))
            .toList();
    Long totalElpCount = filterElpTasks.stream().count();
    Long elpAttemptedCount =
        filterElpTasks.stream()
            .filter(t -> t.getCompletionStatus().equals(TaskStatus.COMPLETED))
            .count();
    List<TestDefinition> totalTests;
    List<TestDefinition> userCompletedTests;
    if (BHARAT_ENGLISH_TEST_ORG.equals(orgSlug)) {
      totalTests =
          testDefinitionRepository.getBetTests(
              "BET-LE-%", TestType.MOCK_TEST.name(), WEXL_INTERNAL);
      userCompletedTests =
          testDefinitionRepository.studentWrittenTest(
              "BET-LE-%", TestType.MOCK_TEST.name(), WEXL_INTERNAL, user.getId());
    } else {
      totalTests =
          testDefinitionRepository.getBetTests(
              "BET-CE-%", TestType.MOCK_TEST.name(), WEXL_INTERNAL);
      userCompletedTests =
          testDefinitionRepository.studentWrittenTest(
              "BET-CE-%", TestType.MOCK_TEST.name(), WEXL_INTERNAL, user.getId());
    }
    List<ExamDto> examsByStudent = examService.findExamsByStudent(orgSlug, studentAuthId);

    Double marksScored = examsByStudent.stream().mapToDouble(ExamDto::getMarksScored).sum();
    Double totalMarks = examsByStudent.stream().mapToDouble(ExamDto::getTotalMarks).sum();
    var percentage = Math.round((marksScored / totalMarks) * 100);

    return DashBoardResponse.DashBoardDetails.builder()
        .userId(user.getId())
        .studentName(userService.getNameByUserInfo(user))
        .userName(user.getUserName())
        .section(
            Objects.nonNull(user.getStudentInfo())
                ? user.getStudentInfo().getSection().getName()
                : null)
        .lastLogin(
            Objects.nonNull(user.getLastLogin())
                ? dateTimeUtil.convertTimeStampToLong(new Timestamp(user.getLastLogin().getTime()))
                : null)
        .totalElpCount(totalElpCount)
        .elpAttemptedCount(elpAttemptedCount)
        .totalMyTestCount((long) totalTests.size())
        .myTestAttemptedCount((long) userCompletedTests.size())
        .percentage(percentage)
        .build();
  }

  public void migrateElpTasks(String orgSlug, ElpDto.ElpTaskMigrationRequest request) {
    var testDefinition =
        testDefinitionRepository
            .findTop1ByTestNameAndOrganizationAndDeletedAtIsNullAndPublishedAtIsNotNull(
                request.newElpSlug(), request.orgSlug())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.TestDefinitionNotFound"));
    var tasks = taskRepository.findAllByOrgSlugAndElpSlug(orgSlug, request.oldElpSlug());
    if (!tasks.isEmpty()) {
      tasks.forEach(
          task -> {
            task.setElpSlug(testDefinition.getTestName());
            task.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
            taskRepository.save(task);
          });
    }
  }

  private final WordRepository wordRepository;

  @Value("${app.wordFinderUrl}")
  private String wordFinderUrl;

  public WordDto.WordResponse getWordMeaning(String word) {
    Optional<Vocabulary> dictionaryWord = wordRepository.findByWord(word);
    final ObjectMapper objectMapper = new ObjectMapper();
    if (dictionaryWord.isEmpty()) {
      RestTemplate restTemplate = new RestTemplate();
      ResponseEntity<List<WordDto.WordResponse>> response =
          restTemplate.exchange(
              wordFinderUrl + word,
              HttpMethod.GET,
              null,
              new ParameterizedTypeReference<List<WordDto.WordResponse>>() {});
      if (response.getBody() != null) {
        var wordDetails = response.getBody().getFirst();
        try {
          String jsonString = objectMapper.writeValueAsString(wordDetails);
          Vocabulary newWord = new Vocabulary();
          newWord.setWord(word);
          newWord.setWordData(jsonString);
          wordRepository.save(newWord);
          return wordDetails;
        } catch (JsonProcessingException e) {
          throw new RuntimeException(e);
        }
      } else {
        return null;
      }
    }
    Vocabulary wordMeaning = dictionaryWord.get();
    try {
      return objectMapper.readValue(wordMeaning.getWordData(), WordDto.WordResponse.class);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
