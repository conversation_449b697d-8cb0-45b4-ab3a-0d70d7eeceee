package com.wexl.retail.zoom.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class JoinEventRequest extends ZoomMeetingEventRequest {

  @JsonProperty("payload")
  private JoinEventPayload payload;

  @Data
  public static class JoinEventPayload {

    @JsonProperty("object")
    private JoinEventObject object;

    @JsonProperty("account_id")
    private String accountId;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class JoinEventObject extends ZoomEventObject {

    @JsonProperty("participant")
    private JoinEvent participant;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class JoinEvent extends ZoomEventParticipantInfo {

    @JsonProperty("join_time")
    private String joinTime;
  }
}
