package com.wexl.retail.student.exam.school;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.student.answer.StudentAnswerRequest;
import com.wexl.retail.student.answer.StudentAnswerResponse;
import com.wexl.retail.student.exam.ExamFactory;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.test.school.dto.TestDefinitionResponse;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/orgs/{orgId}/students/{studentId}")
@IsStudent
@RequiredArgsConstructor
public class SchoolTestExamController {

  private final ExamFactory examFactory;
  private final SchoolExamService schoolExamService;

  @PostMapping("/tests/school")
  public ExamResponse createSchoolTest(@RequestBody SchoolExamRequest examRequest) {

    return schoolExamService.createSchoolTestExam(examRequest);
  }

  @PostMapping("/tests/worksheet")
  public ExamResponse createWorksheetTest(@RequestBody SchoolExamRequest examRequest) {

    return schoolExamService.createExamOfTypeTestDefinition(
        examRequest, examFactory.createWorksheetTest());
  }

  @PostMapping("/tests/assignment")
  public ExamResponse createAssignment(@RequestBody ScheduledExamRequest examRequest) {

    return schoolExamService.createScheduledTestExam(examRequest);
  }

  @PostMapping("/tests/scheduled")
  public ExamResponse createScheduledTest(@RequestBody ScheduledExamRequest examRequest) {

    return schoolExamService.createScheduledTestExam(examRequest);
  }

  @PostMapping("/tests:submit")
  public StudentAnswerResponse submitSchoolTest(
      @RequestBody StudentAnswerRequest studentAnswerRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return schoolExamService.submitSchoolTest(studentAnswerRequest, bearerToken);
  }

  @GetMapping("/tests/{examId}/questions")
  public TestDefinitionResponse getAllSchoolTestQuestions(
      @Valid @PathVariable long examId, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return schoolExamService.getAllSchoolTestQuestions(bearerToken, examId);
  }

  @GetMapping("/tests")
  public List<SchoolScheduledTestResponse> getAllSchoolTests() {

    return schoolExamService.getAllScheduledTests();
  }

  @GetMapping("/assignments")
  public List<SchoolScheduledTestResponse> getScheduledAssignments() {

    return schoolExamService.getScheduledAssignments();
  }
}
