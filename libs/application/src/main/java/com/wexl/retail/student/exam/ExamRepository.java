package com.wexl.retail.student.exam;

import com.wexl.retail.mlp.repository.MlpsQuestionsOptions;
import com.wexl.retail.model.Student;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.school.domain.TestDefinition;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ExamRepository extends JpaRepository<Exam, Long> {

  @Query(
      value =
          """
                      select count(e) from exams e
                      where e.student_id=?1 and e.sub_topic_id=?2 and e.exam_difficulty_level_id=?3
                      and e.exam_type=1
                      """,
      nativeQuery = true)
  long findByStudentIdSubjectIdSubTopicIdChapterId(
      long studentId, long subTopicId, long examDifficultyLevelId);

  @Query(
      value =
          """
                  select count(e) from exams e where
                  e.student_id=?1 and e.exam_type=?2 and
                  e.is_completed=true
                      """,
      nativeQuery = true)
  long findExamCountByStudentId(long studentId, long examType);

  @Query(
      value =
          """
                      select u.organization from exams e inner join students s on e.student_id = s.id
                      inner join users u on s.user_id=u.id where e.id=?1
                      """,
      nativeQuery = true)
  String findStudentOrganizationForExam(long examId);

  @Query(
      value =
          """
                      select u.auth_user_id from exams e inner join students s on e.student_id = s.id
                      inner join users u on s.user_id=u.id where e.id=?1
                      """,
      nativeQuery = true)
  String findStudentAuthIdForExam(long examId);

  @Query("SELECT COUNT(e) FROM Exam e WHERE e.id=?1")
  long findExamExists(long examId);

  Optional<Exam> findById(long examId);

  @Query(
      value =
          """
                      select coalesce(round(max(cast(
                      (e.correct_answers * 100.0 / e.no_of_questions) as float))), 0)
                      from exams e
                      where e.exam_type = 2 and e.is_completed = true and e.correct_answers > 0
                      and e.student_id=?1 and e.chapter_id=?2 and e.exam_difficulty_level_id=?3
                      """,
      nativeQuery = true)
  long findByStudentIdSubjectIdExamTypeIdChapterId(
      long studentId, long chapterId, long examDifficultyLevelId);

  @Query(
      value =
          """
                      select count(e) from exams e where e.student_id=:studentId and
                      e.schedule_test_id = :scheduleTestId and e.is_completed=true
                      """,
      nativeQuery = true)
  long getScheduleTestTakenCountByStudentId(long studentId, long scheduleTestId);

  @Query(
      value =
          """
                      select count(e) from exams e where e.student_id=:studentId and
                      e.test_definition_id=:testDefinitionId and
                      e.is_completed=true
                      """,
      nativeQuery = true)
  long getSurpriseTestTakenByStudentId(long studentId, long testDefinitionId);

  @Query("select count(e) from Exam e where e.student=?1 and e.scheduleTest=?2 ")
  long getPartialTestTakenCountByStudentId(Student student, ScheduleTest scheduleTest);

  @Query(
      value =
          """
                      select extract(epoch from date(e.created_at)) * 1000 as examDate,
                      sum(round(extract(epoch from end_time)) - round(extract(epoch from start_time))) as timeSpent
                      from exams e
                      inner join students s on e.student_id = s.id
                      inner join users u on s.user_id = u.id and u.auth_user_id = :studentAuthId
                      where end_time is not null
                      and extract(epoch from date(start_time)) > :fromTime
                      and extract(epoch from date(end_time)) < :toTime
                      group by date(e.created_at)
                      order by examDate
                      """,
      nativeQuery = true)
  List<UserUsageDetail> getExamsAttendedByStudent(String studentAuthId, long fromTime, long toTime);

  @Query(
      value =
          """
                      select e.* from exams e
                      where e.student_id = :studentId and e.ref = :examRef and end_time is not null and no_of_questions > 0
                      order by e.created_at Asc limit 1
                      """,
      nativeQuery = true)
  Optional<Exam> findByStudentIdAndExamRef(Long studentId, String examRef);

  @Query(
      value =
          """
                      select  mid.mlp_id as mlpId ,ea.question_uuid as questionUuid,
                                  count(case when ea.selected_option = 1 then 1 end) as optionOneCount,
                                  count(case when ea.selected_option = 2 then 1 end) as optionTwoCount,
                                  count(case when ea.selected_option = 3 then 1 end) as optionThreeCount,
                                  count(case when ea.selected_option = 4 then 1 end) as optionFourCount
                      from exam_answers ea
                              inner join mlp_inst mid on mid.exam_id = ea.exam_id
                      where mid.mlp_id = :mlpid
                      group by ea.question_uuid,mid.mlp_id
                      """,
      nativeQuery = true)
  List<MlpsQuestionsOptions> getMlpsExamAnswersByMlpId(Long mlpid);

  @Query(
      value =
          """
              select count(*) from public.exam_revision where student_id = :studentId
              and exam_revision_status = 'NOT_COMPLETED'
              """,
      nativeQuery = true)
  Long getTotalExamQuestionCountNotCompleted(Long studentId);

  @Query(
      value =
          """
                              select id from exams
                              where student_id = :studentId and test_definition_id = :testDefinitionId
                              and course_schedule_item_inst_id = :courseScheduleItemInstId and is_completed=true
                              order by created_at desc
                              """,
      nativeQuery = true)
  List<Long> getExamIdByCourseItemIdAndStudentId(
      long studentId, long testDefinitionId, long courseScheduleItemInstId);

  List<Exam> findByStudentAndRef(Student student, String examRef);

  List<Exam> findTop100ByStudentAndMarksScoredNotNullAndEndTimeNotNullOrderByEndTimeDesc(
      Student student);

  @Query(
      value =
          """
   select * from exams where student_id = :studentId and schedule_test_id = :scheduleTestId
   and end_time is not null limit 1
  """,
      nativeQuery = true)
  Optional<Exam> getExamDetails(Long studentId, Long scheduleTestId);

  List<Exam> findByScheduleTestAndStudentIn(ScheduleTest scheduleTest, List<Student> students);

  List<Exam> findAllByScheduleTestIn(List<ScheduleTest> scheduleTests);

  Optional<Exam> findByIdAndTestDefinitionIsNotNull(long examId);

  List<Exam>
      findTop100ByStudentAndExamTypeInAndMarksScoredNotNullAndEndTimeNotNullOrderByEndTimeDesc(
          Student student, List<Long> examType);

  @Query(
      value =
          """
    SELECT e.*
    FROM exams e
    JOIN proctoring_sessions ps ON ps.exam_id = e.id
    WHERE ps.test_schedule_id = :scheduleTestId
      AND ps.student_id = :studentId
    ORDER BY ps.end_time DESC
    LIMIT 1
    """,
      nativeQuery = true)
  Optional<Exam> findLatestExamByScheduleTestIdAndStudentId(
      @Param("scheduleTestId") Long scheduleTestId, @Param("studentId") Long studentId);

  Optional<Exam> findByStudentAndTaskId(Student student, long taskId);

  @Query(
      value =
          """
          select e.* from  exams  e
          join test_schedule ts  on ts.id =e.schedule_test_id
          join test_definitions td on td.id = ts.test_definition_id
          where e.student_id =:studentId and td.category in(:categories)""",
      nativeQuery = true)
  List<Exam> getExamsByStudentAndTestCategories(Long studentId, List<Integer> categories);

  Optional<Exam> findTop1ByStudentAndTestDefinition(
      Student studentInfo, TestDefinition testDefinition);
}
