package com.wexl.retail.organization.model;

import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.mobile.model.MobileConfig;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.dto.Attributes;
import com.wexl.retail.organization.dto.Curriculum;
import com.wexl.retail.organization.dto.OrgMetaData;
import com.wexl.retail.organization.reports.dto.SubjectsMetaDataDto;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@Entity
@Table(
    name = "orgs",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"slug"})})
@EntityListeners(AuditingEntityListener.class)
public class Organization extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  private String slug;

  @OneToOne
  @JoinColumn(name = "parent_id")
  private Organization parent;

  @OneToOne
  @JoinColumn(name = "publisher_id")
  private Organization publisher;

  private Boolean status;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private OrgMetaData metadata;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private Curriculum curriculum;

  @Column(name = "is_publisher", columnDefinition = "boolean default false")
  private Boolean isPublisher;

  @Column(name = "is_parent", columnDefinition = "boolean default false")
  private Boolean isParent;

  @Column(name = "abbreviation")
  private String abbreviation;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "createdBy")
  private User createdBy;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "mobile_config_id")
  private MobileConfig mobileConfig;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private Attributes attributes;

  @OneToOne
  @JoinColumn(name = "global_profile_id")
  private GlobalProfile profile;

  private Boolean selfSignup;

  private Boolean settingsMigration = Boolean.FALSE;

  @Column(name = "logo")
  private String logo;

  @Column(name = "theme")
  private String theme;

  @Column(name = "website")
  private String website;

  @Column(name = "mobile_logo")
  private String mobileLogo;

  @Column(name = "student_url")
  private String studentUrl;

  @Column(name = "logout_url")
  private String logoutUrl;

  @Column(name = "org_profile")
  private Long orgProfile;

  private Boolean sendSms = Boolean.FALSE;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private SubjectsMetaDataDto.GradesData reportsMetadata;

  @Column(name = "is_dackm")
  private Boolean dacKmFlag;

  @Column(name = "is_corporate")
  private Boolean isCorporate;
}
