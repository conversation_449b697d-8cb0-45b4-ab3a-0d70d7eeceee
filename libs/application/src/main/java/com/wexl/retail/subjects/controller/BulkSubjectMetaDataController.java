package com.wexl.retail.subjects.controller;

import com.wexl.retail.admin.ImportResponse;
import com.wexl.retail.subjects.model.SubjectsDto.BulkRequest;
import com.wexl.retail.subjects.service.SubjectsMetaDataService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
@Slf4j
public class BulkSubjectMetaDataController {

  private final SubjectsMetaDataService subjectsMetaDataService;

  @PostMapping("/subjects-metadata:bulk")
  public ImportResponse associateSubjectsToStudent(
      @PathVariable String orgSlug, @Valid @RequestBody BulkRequest bulkRequest) {
    try {
      final List<String> strings =
          subjectsMetaDataService.associateSubjectsToStudent(orgSlug, bulkRequest);
      final String result = strings.stream().reduce((s1, s2) -> s1 + "," + s2).orElse("");
      return ImportResponse.builder().importStatus("SUCCESS").importErrorMessage(result).build();
    } catch (Exception ex) {
      log.error("Error occured", ex);
      return ImportResponse.builder()
          .importStatus("FAILED")
          .importErrorMessage(ex.getMessage())
          .build();
    }
  }
}
