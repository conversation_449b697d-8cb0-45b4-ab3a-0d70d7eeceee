package com.wexl.retail.student.exam;

import com.wexl.retail.student.answer.ExamAnswer;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

@Repository
public interface ExamAnswerRepository extends JpaRepository<ExamAnswer, Long> {
  List<ExamAnswer> findByExam(Exam exam);

  @Modifying
  @Transactional
  void deleteByExam(Exam exam);
}
