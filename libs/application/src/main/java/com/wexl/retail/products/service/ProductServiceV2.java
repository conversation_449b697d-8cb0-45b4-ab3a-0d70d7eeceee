package com.wexl.retail.products.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.bundles.repository.CourseBundleRepository;
import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.ecommerce.ProductDto;
import com.wexl.retail.ecommerce.ProductDto.ProductResponse;
import com.wexl.retail.ecommerce.ProductDto.ProductResponseList;
import com.wexl.retail.products.dto.CourseDto;
import com.wexl.retail.products.model.ProductCourses;
import com.wexl.retail.products.repository.ProductCourseRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
public class ProductServiceV2 {

  private final RestTemplate restTemplate;
  private final ProductCourseRepository productCourseRepository;
  private final CourseBundleRepository courseBundleRepository;
  private final CourseDefinitionRepository courseDefinitionRepository;

  @Value(
      "${app.ecommerce.service.url:http://product-service.dev.svc.cluster.local:8080/api/products/}")
  public String productServiceUrl;

  public List<ProductResponse> getProductsForOrg(String orgSlug, String bearerToken) {
    String endPoint = "%sorgs/%s/products".formatted(productServiceUrl, orgSlug);
    final ProductResponseList productResponseList =
        getHttpEntity(endPoint, ProductResponseList.class, bearerToken);

    if (productResponseList == null
        || productResponseList.productResponses() == null
        || productResponseList.productResponses().isEmpty()) {
      return new ArrayList<>();
    }
    return buildProductResponse(productResponseList.productResponses(), orgSlug);
  }

  private List<ProductDto.ProductResponse> buildProductResponse(
      List<ProductDto.ProductResponse> products, String orgSlug) {
    List<ProductDto.ProductResponse> productList = new ArrayList<>();
    products.forEach(
        product -> {
          var courseDef =
              productCourseRepository.findByproductRefAndOrgSlug(product.extRef(), orgSlug);
          ProductDto.CourseDefinitions coursesDefIds = null;
          if (courseDef != null) {
            coursesDefIds =
                ProductDto.CourseDefinitions.builder()
                    .courseDefinitions(courseDef.getCourseDefinitionId())
                    .build();
          }

          productList.add(
              ProductDto.ProductResponse.builder()
                  .id(Long.valueOf(product.extRef()))
                  .description(product.description())
                  .status(product.status() == null ? "" : product.status().toUpperCase())
                  .thumbNail(product.thumbNail())
                  .courseDefinitions(coursesDefIds)
                  .title(product.title())
                  .extRef(product.extRef())
                  .build());
        });
    return productList;
  }

  public ProductDto.Banner getProductBanners(String orgSlug, String bearerToken) {
    String endPoint = "%sorgs/%s/products-banner".formatted(productServiceUrl, orgSlug);
    return getHttpEntity(endPoint, ProductDto.Banner.class, bearerToken);
  }

  public ProductResponse getProductByExtRef(String orgSlug, String extRef, String bearerToken) {
    String endPoint = "%sorgs/%s/products/%s".formatted(productServiceUrl, orgSlug, extRef);
    return getHttpEntity(endPoint, ProductResponse.class, bearerToken);
  }

  private <T> T getHttpEntity(String endPoint, Class<T> responseType, String bearerToken) {
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    var httpEntity = new HttpEntity<>(null, headers);
    return restTemplate.exchange(endPoint, HttpMethod.GET, httpEntity, responseType).getBody();
  }

  public void associateCourseToProduct(
      CourseDto.CourseRequest courseRequest, String productRef, String orgSlug) {
    if (courseRequest.courseRequest() == null && courseRequest.courseBundleIds() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.CourseIdsNotBeNullOrEmpty");
    }
    validateCourses(courseRequest.courseRequest());
    validateCourseBundles(courseRequest.courseBundleIds());
    var productCourses = productCourseRepository.findByproductRefAndOrgSlug(productRef, orgSlug);
    if (Objects.nonNull(productCourses)) {
      productCourses.setCourseDefinitionId(courseRequest.courseRequest());
      productCourses.setCourseBundleIds(courseRequest.courseBundleIds());
      productCourses.setOrgSlug(orgSlug);
      productCourseRepository.save(productCourses);
      return;
    }
    ProductCourses productCourse = new ProductCourses();
    productCourse.setProductRef(productRef);
    productCourse.setCourseDefinitionId(courseRequest.courseRequest());
    productCourse.setCourseBundleIds(courseRequest.courseBundleIds());
    productCourse.setOrgSlug(orgSlug);
    productCourseRepository.save(productCourse);
  }

  private void validateCourseBundles(List<Long> ids) {
    if (ids != null && !ids.isEmpty()) {
      var courseBundles = courseBundleRepository.findAllById(ids);

      if (courseBundles.isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidCourseBundle");
      }
    }
  }

  private void validateCourses(List<Long> ids) {
    var courseDefinitions = courseDefinitionRepository.findAllById(ids);
    var courseDataIds = courseDefinitions.stream().map(CourseDefinition::getId).toList();

    List<Long> invalidCourseIds =
        ids.stream().filter(element -> !courseDataIds.contains(element)).toList();
    if (!invalidCourseIds.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.InvalidCourseIds",
          new String[] {invalidCourseIds.toString()});
    }
  }

  public List<ProductResponse> getProductsByStudentId(
      String orgSlug, String studentAuthId, String bearerToken) {
    String endPoint =
        "%sorgs/%s/users/%s/orders".formatted(productServiceUrl, orgSlug, studentAuthId);
    final ProductResponseList productResponseList =
        getHttpEntity(endPoint, ProductResponseList.class, bearerToken);
    if (productResponseList == null
        || productResponseList.productResponses() == null
        || productResponseList.productResponses().isEmpty()) {
      return new ArrayList<>();
    }
    return productResponseList.productResponses();
  }
}
