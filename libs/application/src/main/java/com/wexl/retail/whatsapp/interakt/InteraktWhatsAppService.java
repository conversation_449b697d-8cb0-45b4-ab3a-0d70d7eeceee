package com.wexl.retail.whatsapp.interakt;

import com.wexl.retail.whatsapp.WhatsAppService;
import com.wexl.retail.whatsapp.interakt.dto.Request.Recipient;
import com.wexl.retail.whatsapp.interakt.dto.Request.SendMessageRequest;
import com.wexl.retail.whatsapp.interakt.dto.Request.SendMessageResponse;
import com.wexl.retail.whatsapp.interakt.dto.Request.WhatsappMessageTemplate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
@Slf4j
public class InteraktWhatsAppService implements WhatsAppService {

  private final RestTemplate restTemplate;
  private static final String INTERAKT_API_URL = "https://api.interakt.ai/v1/public/message/";

  @Value("${app.whatsapp.token:b1JMcVh6VDBLeEZNZFlYT1VlQVNOeG8yWmJ6Ri1kVjhMYnpNZWVyYmoyYzo=}")
  private String apiToken;

  @Override
  public void sendWhatsAppMessage(String templateId, List<Recipient> recipientsOriginal) {
    var recipients = recipientsOriginal;
    if (recipientsOriginal.size() > 3) {
      recipients = recipientsOriginal.subList(0, 2);
    }
    String authorizationToken = "Basic " + apiToken;
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, authorizationToken);
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");

    recipients.forEach(
        recipient -> {
          var mobileNumber = recipient.mobileNumber();
          if (recipient.mobileNumber().startsWith("+91")) {
            mobileNumber = recipient.mobileNumber().substring(3);
          }
          if (mobileNumber.length() != 10) {
            log.error("Invalid mobile number:{} for recipient: {}", mobileNumber, recipient);
            return;
          }
          final SendMessageRequest sendMessageRequest =
              SendMessageRequest.builder()
                  .countryCode("+91")
                  .phoneNumber(mobileNumber)
                  .type("Template")
                  .template(
                      WhatsappMessageTemplate.builder()
                          .name(templateId)
                          .languageCode("en")
                          .bodyValues(List.of("Test"))
                          .build())
                  .build();

          final HttpEntity<SendMessageRequest> requestEntity =
              new HttpEntity<>(sendMessageRequest, headers);

          var response =
              restTemplate.postForObject(
                  INTERAKT_API_URL, requestEntity, SendMessageResponse.class);
          log.info("Successfully sent message {}", response);
        });
  }
}
