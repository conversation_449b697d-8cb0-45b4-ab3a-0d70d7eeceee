package com.wexl.retail.department.repository;

import com.wexl.retail.department.model.Department;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {
  List<Department> findAllByOrgSlug(String orgSlug);

  Optional<Department> findByNameAndOrgSlug(String departmentName, String orgSlug);
}
