package com.wexl.retail.test.school.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.liveworksheet.dto.WorkSheetQuestionType;
import com.wexl.retail.liveworksheet.dto.WorkSheetType;
import com.wexl.retail.metrics.dto.QuestionDetails;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record QuestionDto() {

  @Builder
  public record QuestionResponse(
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("test_name") String testName,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("duration") Long duration,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("test_definition_section")
          List<TestDefinitionSectionResponse> testDefinitionSectionResponses,
      @JsonProperty("total_marks") int totalMarks,
      @JsonProperty("instructions") String instructions,
      @JsonProperty("asset_slug") String assetSlug,
      @JsonProperty("answer_key") List<SectionAnswerKey> answerKey) {}

  @Builder
  public record SectionAnswerKey(
      @JsonProperty("section_name") String name,
      @JsonProperty("answer_keys") List<Answer> answerKeys) {}

  @Builder
  public record Answer(
      String questionNumber,
      String type,
      String answer,
      @JsonProperty("pbq_answers") PbqDto.Data pbqAnswers,
      List<Long> msqAnswer,
      List<String> ddFbqAnswers) {}

  @Builder
  public record TestDefinitionSectionResponse(
      Long id,
      String name,
      Long noOfQuestions,
      Long seqNo,
      List<Question> questions,
      int marks,
      String instructions) {}

  @Builder
  public record StudentQuestionStatusResponse(
      @JsonProperty("type") QuestionType questionType,
      @JsonProperty("tssa_uuid") String tssaUuid,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("test_question_marks") Integer testQuestionMarks,
      @JsonProperty("mcq_selected_answer") Integer mcqSelectedAnswer,
      @JsonProperty("msq_selected_answer") List<Long> msqSelectedAnswer,
      @JsonProperty("subjective_selected_answer") String subjectiveWrittenAnswer,
      @JsonProperty("nat_selected_answer") Float natSelectedAnswer,
      @JsonProperty("yes_no_selected_answer") Boolean yesNoSelectedAnswer,
      @JsonProperty("fbq_selected_answer") String fbqSelectedAnswer,
      @JsonProperty("pbq_selected_answer") PbqDto.Data pbqSelectedAnswer,
      @JsonProperty("amcq_selected_answer") Integer amcqSelectedAnswer,
      @JsonProperty("spch_selected_answer") String spchSelectedAnswer,
      @JsonProperty("ddfbq_attempted_answer") String ddFbqAttemptedAnswer,
      @JsonProperty("ai_analysis") String aiAnalysis,
      @JsonProperty("ai_marks") Float aiMarks,
      StudentTestAttemptStatus status) {}

  @Builder
  public record Question(
      Integer id,
      @JsonProperty("test_section_id") Long testSectionId,
      String uuid,
      String question,
      Integer marks,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      QuestionType type,
      String complexity,
      String explanation,
      String category,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("blooms_taxonomy_id") Integer bloomsTaxonomyId,
      @JsonProperty("blooms_taxonomy") String bloomsTaxonomy,
      List<String> questionTags,
      Boolean active,
      Boolean published,
      @JsonProperty("organization_slug") String organizationSlug,
      String organization,
      @JsonProperty("negative_marks") float negativeMarks,
      Mcq mcq,
      Msq msq,
      Subjective subjective,
      YesNo yesNo,
      List<Pbq> pbq,
      Fbq fbq,
      Nat nat,
      Amcq amcq,
      Spch spch,
      DdFbq ddFbq,
      @JsonProperty("audio_path") String audioPath,
      @JsonProperty("video_path") String videoPath,
      @JsonProperty("internal_choice") Question internalChoice,
      @JsonProperty("worksheet") List<WorkSheet> worksheet,
      @JsonProperty("question_category_id") String questionCategoryId,
      @JsonProperty("video_asset_link") String videoAssetLink,
      @JsonProperty("asset_id") Long assetId,
      @JsonProperty("sha_link") String shaLink) {}

  @Builder
  public record QuestionRequest(
      String question,
      Integer marks,
      Boolean active,
      String organization,
      QuestionType type,
      String complexity,
      String explanation,
      Mcq mcq,
      String uuid,
      @JsonProperty("question_tags") List<String> questionTags,
      @JsonProperty("question_category_id") @NotNull String category,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("blooms_taxonomy_id") Integer bloomsTaxonomyId,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      @JsonProperty("worksheet") List<WorkSheet> worksheet) {}

  @Builder
  public record WorkSheet(
      Long id,
      String uuid,
      @JsonProperty("worksheet_type") WorkSheetType workSheetType,
      @JsonProperty("worksheet_questions") WorkSheetQuestion workSheetQuestion) {}

  @Builder
  public record WorkSheetQuestion(
      String question,
      String answer,
      @JsonProperty("question_type") WorkSheetQuestionType workSheetQuestionType,
      @JsonProperty("answer_type") WorkSheetQuestionType workSheetAnswerType,
      @JsonProperty("answer_uuid") String answerUuid,
      Integer marks) {}

  @Builder
  public record Mcq(String option1, String option2, String option3, String option4, Long answer) {}

  @Builder
  public record Msq(
      String option1, String option2, String option3, String option4, List<Long> answers) {}

  @Builder
  public record YesNo(
      @JsonProperty("yes_label") String yesLabel,
      @JsonProperty("no_label") String noLabel,
      Boolean answer) {}

  @Builder
  public record Nat(Float answer) {}

  @Builder
  public record Subjective(String explanation) {}

  @Builder
  public record Fbq(String answer) {}

  @Builder
  public record DdFbq(
      @JsonProperty("question_and_answer") String questionAndAnswer, List<String> words) {}

  @Builder
  public record Pbq(
      String question,
      Integer marks,
      Mcq mcq,
      String explanation,
      @JsonProperty("pbq_selected_answer") Integer selectedAnswer,
      Long id,
      String uuid,
      QuestionType type) {}

  @Builder
  public record SearchQuestionResponse(List<Question> questions) {}

  @Builder
  public record QuestionsResponse(
      @JsonProperty("instructions") String Instructions,
      Long duration,
      @JsonProperty("student_question_status_response")
          List<StudentQuestionStatusResponse> StudentQuestionStatusResponse) {}

  @Builder
  public record StudentResultsResponse(
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("test_name") String testName,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("exam_id") long examId,
      @JsonProperty("attempted_questions") Integer attemptedQuestions,
      @JsonProperty("total_marks") Float totalMarks,
      @JsonProperty("total_marks_secured") Float totalMarksSecured,
      @JsonProperty("percentage_secured") Float percentageSecured,
      @JsonProperty("asset_slug") String assetSlug,
      @JsonProperty("explanation_video_sha") String explanationVideoSha,
      @JsonProperty("question_paper_ref") String questionPaperRef,
      @JsonProperty("answers_paper_ref") String answersPaperRef,
      @JsonProperty("image_path") String omrImagePath,
      @JsonProperty("has_pdf_answer_sheet") boolean hasPdfAnswerSheet,
      @JsonProperty("pdf_answer_sheet_url") String pdfAnswerSheetUrl,
      @JsonProperty("upload_corrected_answer_sheet_url") String uploadCorrectedAnswerSheetUrl,
      @JsonProperty("test_definition_section") List<TestDefinitionSection> testDefinitionSection) {}

  @Builder
  public record QuestionResult(
      @JsonProperty("question_seq_no") Integer questionSeqNo,
      Integer id,
      String uuid,
      String question,
      @JsonProperty("audio_path") String audioPath,
      @JsonProperty("video_path") String videoPath,
      Integer marks,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      QuestionType type,
      String complexity,
      String explanation,
      String category,
      @JsonProperty("question_tags") String questionTags,
      @JsonProperty("is_corrected") Boolean isCorrected,
      @JsonProperty("is_attempted") Boolean isAttempted,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("mcq_selected_answer") Long mcqSelectedAnswer,
      @JsonProperty("yes_no_selected_answer") Boolean yesNoSelectedAnswer,
      @JsonProperty("nat_selected_answer") Float natSelectedAnswer,
      @JsonProperty("subjective_selected_answer") String subjectiveSelectedAnswer,
      @JsonProperty("ai_marks") Float aiMarks,
      @JsonProperty("ai_analysis") String aiAnalysis,
      @JsonProperty("fbq_selected_answer") String fbqSelectedAnswer,
      @JsonProperty("msq_selected_answer") List<Long> msqSelectedAnswer,
      @JsonProperty("spch_selected_answer") String spchSelectedAnswer,
      @JsonProperty("amcq_selected_answer") Integer amcqSelectedAnswer,
      @JsonProperty("ddfbq_selected_answer") String ddFbqSelectedAnswer,
      @JsonProperty("marks_secured") Float marksSecured,
      @JsonProperty("negative_marks") float negativeMarks,
      Mcq mcq,
      Msq msq,
      Subjective subjective,
      YesNo yesNo,
      List<Pbq> pbq,
      Nat nat,
      Fbq fbq,
      Amcq amcq,
      Spch spch,
      DdFbq ddFbq) {}

  @Builder
  public record TestDefinitionSection(
      @JsonProperty("tss_id") Long id,
      @JsonProperty("tss_name") String name,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      Long seqNo,
      Double marks,
      @JsonProperty("total_questions") Long totalQuestions,
      List<QuestionResult> questionResults) {}

  @Builder
  public record MockExamAnalyticResponse(
      @JsonProperty("question_response") QuestionResponse questionResponse,
      @JsonProperty("question_details") List<QuestionDetails> questionDetails,
      @JsonProperty("teacher_name") String teacherName,
      String title,
      @JsonProperty("created_date") Long createdDate,
      @JsonProperty("total_students_count") Integer studentCount,
      @JsonProperty("attempted_student_count") Integer attemptedCount) {}

  @Builder
  public record Amcq(
      @JsonProperty("audio_path") String audioPath,
      @JsonProperty("video_slug") String videoAssetSlug,
      @JsonProperty("video_path") String videoPath,
      @JsonProperty("video_url") String videoAssetUrl,
      @JsonProperty("sha_link") String shaLink,
      @JsonProperty("is_audio") Boolean isAudio,
      String option1,
      String option2,
      String option3,
      String option4,
      Integer answer) {}

  @Builder
  public record Spch(
      @JsonProperty("answer_audio_path") String answerAudioPath,
      @JsonProperty("explanation_audio_path") String explanationAudioPath) {}

  @Builder
  public record TestSectionDetails(
      String testName,
      String scheduleDate,
      String studentName,
      List<SectionWiseMarks> sectionWiseMarks) {}

  @Builder
  public record SectionWiseMarks(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("total_marks") Double totalMarks,
      @JsonProperty("secured_marks") Double securedMarks,
      Long attempted,
      @JsonProperty("not_attempted") Long notAttempted) {}

  @Builder
  public record SearchQuestionByUuidResponse(String uuid, String answer) {}

  @Builder
  public record SearchQuestionByUuid(List<String> uuid, QuestionType type) {}

  @Builder
  public record TestQuestionResponse(Long testDefinitionId, List<SectionResponse> sections) {}

  @Builder
  public record SectionResponse(
      Long sectionId,
      String sectionName,
      String chapterName,
      String chapterSlug,
      Integer marks,
      Integer questionCount,
      QuestionType type,
      String category,
      String complexity,
      String questionTag) {}

  @Builder
  public record SectionQpGenRequest(
      Long sectionId,
      String sectionName,
      String subjectSlug,
      String chapterName,
      String chapterSlug,
      String questionType,
      String questionCategory,
      String questionCategoryName,
      String complexity,
      String complexityName) {}

  @Builder
  public record SectionQpGenResponse(QpGenChapterQuestions chapterQuestions) {}

  @Builder
  public record QpGenChapterQuestions(
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("question_type") String questionType,
      @JsonProperty("question_category_name") String questionCategoryName,
      @JsonProperty("question_category_slug") String questionCategorySlug,
      @JsonProperty("question_complexity_name") String questionComplexityName,
      @JsonProperty("question_complexity_slug") String questionComplexitySlug,
      @JsonProperty("total_available_questions") Long totalAvailableQuestions,
      @JsonProperty("selected_chapter_questions") Long selectedChapterQuestions,
      @JsonProperty("total_1m_questions") Long total1mQuestions,
      @JsonProperty("selected_1m_questions") Long selected1mQuestions,
      @JsonProperty("total_2m_questions") Long total2mQuestions,
      @JsonProperty("selected_2m_questions") Long selected2mQuestions,
      @JsonProperty("total_3m_questions") Long total3mQuestions,
      @JsonProperty("selected_3m_questions") Long selected3mQuestions,
      @JsonProperty("total_4m_questions") Long total4mQuestions,
      @JsonProperty("selected_4m_questions") Long selected4mQuestions,
      @JsonProperty("total_5m_questions") Long total5mQuestions,
      @JsonProperty("selected_5m_questions") Long selected5mQuestions,
      @JsonProperty("test_definition_section_id") Long testDefinitionSectionId) {}

  @Builder
  public record SubmitMockExamResponse(@JsonProperty("exam_id") Long examId) {}

  public record QuestionTags(String type, List<tags> tag) {}

  @Builder
  public record tags(String name, String code, List<complexities> complexities) {}

  @Builder
  public record complexities(String name, String value) {}

  @Builder
  public record StudentPersonalizedWorkSheetResponse(
      List<TestEnrichmentResponse> testEnrichmentResponses) {}

  @Builder
  public record TestEnrichmentResponse(
      ExamAnalysis.TestEnrichData testEnrichData,
      String summary,
      String references,
      String concept,
      String questionUuid) {}

  @Builder
  public record ValidateAnswerResponse(
      @JsonProperty("is_correct") Boolean isCorrect,
      String uuid,
      String question,
      Integer marks,
      QuestionType type,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      String explanation,
      @JsonProperty("negative_marks") float negativeMarks,
      String category,
      Mcq mcq,
      Msq msq,
      Subjective subjective,
      YesNo yesNo,
      List<Pbq> pbq,
      Fbq fbq,
      Nat nat,
      Amcq amcq,
      Spch spch,
      DdFbq ddFbq,
      @JsonProperty("audio_path") String audioPath,
      @JsonProperty("video_path") String videoPath,
      @JsonProperty("marks_scored") Float marksScored) {}

  @Builder
  public record BetAnswerValidateRequest(
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("type") QuestionType questionType,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("mcq_selected_answer") Integer mcqSelectedAnswer,
      @JsonProperty("msq_selected_answer") List<Long> msqSelectedAnswer,
      @JsonProperty("subjective_written_answer") String subjectiveWrittenAnswer,
      @JsonProperty("nat_selected_answer") Float natSelectedAnswer,
      @JsonProperty("yes_no_selected_answer") Boolean yesNoSelectedAnswer,
      @JsonProperty("fbq_selected_answer") String fbqSelectedAnswer,
      @JsonProperty("pbq_selected_answer") PbqDto.Data pbqSelectedAnswer,
      @JsonProperty("amcq_selected_answer") Integer amcqSelectedAnswer,
      @JsonProperty("spch_selected_answer") String spchSelectedAnswer,
      @JsonProperty("ddfbq_attempted_answer") String ddFbqAttemptedAnswer) {}

  public record UploadQuestionsRequest(
      String sectionName,
      Integer questionNo,
      String question,
      String type,
      String answer,
      String explanation,
      String option1,
      String option2,
      String option3,
      String option4,
      String image,
      Integer marks,
      String uuid) {}

  @Builder
  public record contentRequest(List<QuestionDto.UploadQuestionsRequest> uploadedQuestions) {}

  public record UploadQuestionsResponse(
      List<QuestionDto.UploadQuestionsRequest> uploadedQuestions,
      List<QuestionDto.Question> questions) {}
}
