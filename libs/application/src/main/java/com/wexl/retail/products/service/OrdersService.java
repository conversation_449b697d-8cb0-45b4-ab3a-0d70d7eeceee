package com.wexl.retail.products.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.ecommerce.ProductDto;
import com.wexl.retail.ecommerce.ProductDto.ProductResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.products.repository.ProductCourseRepository;
import com.wexl.retail.repository.StudentRepository;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class OrdersService {

  private final AuthService authService;
  private final StudentRepository studentRepository;
  private final ProductCourseRepository productCourseRepository;
  private final ProductCourseService productCourseService;
  private final ProductServiceV2 productService;

  public List<ProductDto.ProductOrdersResponse> getProductsByStudentId(
      String orgSlug, String studentAuthId, String bearerToken) {
    User user = authService.getUserByAuthUserId(studentAuthId);
    Student student = studentRepository.findByUserId(user.getId());
    if (!student.getUserInfo().getOrganization().equals(orgSlug)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidStudentId");
    }

    final List<ProductResponse> productResponseList =
        productService.getProductsByStudentId(orgSlug, studentAuthId, bearerToken);

    return buildProductOrdersResponse(productResponseList, orgSlug);
  }

  private List<ProductDto.ProductOrdersResponse> buildProductOrdersResponse(
      List<ProductResponse> productResponses, String orgSlug) {
    if (productResponses.isEmpty()) {
      return Collections.emptyList();
    }
    List<ProductDto.ProductOrdersResponse> orderList = new ArrayList<>();
    productResponses.forEach(
        product -> {
          var productCourses =
              productCourseRepository.findByproductRefAndOrgSlug(product.extRef(), orgSlug);
          if (productCourses != null) {
            var courses =
                ProductDto.CourseDefinitions.builder()
                    .courseDefinitions(productCourses.getCourseDefinitionId())
                    .build();
            orderList.add(
                ProductDto.ProductOrdersResponse.builder()
                    .productId(product.extRef())
                    .title(product.title())
                    .courseDefinitions(courses)
                    .productKm(buildProductsKm(product, orgSlug))
                    .thumbnail(product.thumbNail())
                    .description(product.description())
                    .build());
          }
        });

    return orderList;
  }

  private Double buildProductsKm(ProductResponse productResponse, String orgSlug) {
    var response = productCourseService.getCoursesByProductId(productResponse, orgSlug);
    return response.productKm();
  }
}
