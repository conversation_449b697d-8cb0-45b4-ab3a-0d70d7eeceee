package com.wexl.retail.products.dto;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "app.ecommerce")
public class StoreConfiguration {

  private List<Store> stores;

  @Data
  public static class Store {

    private String slug;
    private String type;
    private String token;
    private String name;
    private String productId;
    private String androidAppUrl;
    private String webUrl;
  }
}
