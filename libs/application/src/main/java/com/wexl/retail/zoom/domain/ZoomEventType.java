package com.wexl.retail.zoom.domain;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ZoomEventType {
  PARTICIPANT_JOIN_EVENT("meeting.participant_joined"),
  PARTICIPANT_LEFT_EVENT("meeting.participant_left"),
  MEETING_STARTED_EVENT("meeting.started"),
  MEETING_ENDED_EVENT("meeting.ended");

  private final String value;

  public static ZoomEventType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (ZoomEventType enumEntry : ZoomEventType.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
