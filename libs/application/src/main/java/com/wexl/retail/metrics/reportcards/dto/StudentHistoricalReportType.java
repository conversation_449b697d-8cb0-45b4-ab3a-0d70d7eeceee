package com.wexl.retail.metrics.reportcards.dto;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "student_historical_report_type")
public class StudentHistoricalReportType extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "test_definition_id")
  private Long testDefinitionId;

  @Column(name = "report_type")
  private String reportType;
}
