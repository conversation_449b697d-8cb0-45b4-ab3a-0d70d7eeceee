package com.wexl.retail.urls.controller;

import com.wexl.retail.urls.dto.UrlsDto;
import com.wexl.retail.urls.service.UrlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/urls")
public class UrlsController {

  private final UrlService urlService;

  @PostMapping()
  public UrlsDto.UrlResponse createScormUrl(@RequestBody UrlsDto.UrlRequest request) {

    return urlService.createScormUrl(request);
  }
}
