package com.wexl.retail.zoom.repository;

import com.wexl.retail.zoom.domain.ParticipantEvent;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ParticipantEventRepository extends JpaRepository<ParticipantEvent, Long> {

  @Query(
      value =
          """
          select * \
          from event_participant \
          where start_time = :startTime and external_event_id = :externalEventId \
          and customer_key = :customerKey\
          """,
      nativeQuery = true)
  ParticipantEvent getParticipantEventByExternalEventIdAndCustomerKeyAndStartTime(
      Timestamp startTime, String externalEventId, String customerKey);

  @Query(
      value =
          """
          select *\s
          from event_participant
          where customer_key = :customerKey
             and timestamp_cmp_date(date(start_time), :currentDate) = 0
             and external_event_id = :meetingId\
          """,
      nativeQuery = true)
  List<ParticipantEvent> getAttendeeMeetingEvents(
      String customerKey, Date currentDate, String meetingId);
}
