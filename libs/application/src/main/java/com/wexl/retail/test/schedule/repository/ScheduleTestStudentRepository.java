package com.wexl.retail.test.schedule.repository;

import com.wexl.retail.model.User;
import com.wexl.retail.student.exam.school.AllScheduledTests;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.ScheduleTestStudentAndStatus;
import com.wexl.retail.test.schedule.dto.ScheduleTestStudentResponse;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.dto.TestDetailsUser;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduleTestStudentRepository extends JpaRepository<ScheduleTestStudent, Long> {

  List<ScheduleTestStudent> findByScheduleTest(ScheduleTest scheduleTest);

  @Query(
      value =
          """
            select ts.id as scheduleTestId, td.id as testDefinitionId, td.test_name
                   as testName, ts.start_date as startDate, ts.end_date as endDate,td.type as testType,
                   td.subject_slug as subjectSlug, tss.status as testState, tss.uuid as scheduleTestUuid
                from test_schedule_student tss
                left join test_schedule ts on ts.id = tss.schedule_test_id
                left join test_definitions td on ts.test_definition_id = td.id
                where ts.start_date between now() - INTERVAL '90 DAY' and now() + INTERVAL '90 DAY'
                  and ts.status = 'active' and td.type  in (:types)
                  and tss.student_id = :studentId  and tss.status IN ('PENDING','STARTED') and ts.end_date > now()
                order by start_date desc""",
      nativeQuery = true)
  List<AllScheduledTests> findAllTestsForStudent(long studentId, List<String> types);

  @Query(
      value =
          """
            select ts.id as scheduleTestId, td.id as testDefinitionId,
            td.test_name as testName, ts.start_date as startDate,
            ts.end_date as endDate, td.subject_slug as subjectSlug,
            tss.status as testState
            from test_schedule_student tss
            left join test_schedule ts on ts.id = tss.schedule_test_id
            left join test_definitions td on ts.test_definition_id = td.id
            where ts.status = 'active' and td.type = 'ASSIGNMENT'
            and tss.student_id = :studentId and tss.status in ('PENDING','STARTED') and ts.end_date > now()
            order by start_date
            """,
      nativeQuery = true)
  List<AllScheduledTests> getScheduledAssignments(long studentId);

  @Query(
      value =
          """
            select * from test_schedule_student tss \
            where tss.student_id = :studentId and \
            tss.schedule_test_id=:scheduleTestId and tss.status='STARTED' \
            """,
      nativeQuery = true)
  Optional<ScheduleTestStudent> findByTestScheduleIdStudentId(long studentId, long scheduleTestId);

  @Query(
      value =
          """
            SELECT tss.schedule_test_id AS scheduleTestId,u.id AS userId,u.user_name AS userName,u.first_name AS firstName,
            u.last_name AS lastName,s.id AS studentId,s.roll_number as rollNumber,s.class_roll_number as classRollNumber,(SELECT tss1.status FROM test_schedule_student tss1
            WHERE tss1.schedule_test_id = tss.schedule_test_id AND tss1.student_id=tss.student_id)as studentTestStatus,
            CASE WHEN (SELECT tss1.status FROM test_schedule_student tss1 WHERE tss1.schedule_test_id = tss.schedule_test_id
            AND tss1.student_id=tss.student_id ORDER BY id DESC LIMIT 1) = 'COMPLETED' THEN TRUE ELSE FALSE END AS testTaken,
            _section.id AS sectionId,_section.name AS sectionName,e.id AS examId,e.corrected AS examEvaluated,s.school_name AS instituteName
            FROM (SELECT schedule_test_id, student_id FROM test_schedule_student GROUP BY schedule_test_id, student_id) tss
            INNER JOIN users u ON u.id = tss.student_id INNER JOIN students s ON s.user_id = u.id
            LEFT OUTER JOIN exams e ON e.schedule_test_id = tss.schedule_test_id AND e.student_id = s.id AND e.end_time is not null
            INNER JOIN sections _section ON _section.id = s.section_id LEFT JOIN teacher_sections ts ON ts.section_id = _section.id
            AND ts.teacher_id = (SELECT id FROM teacher_details td WHERE td.user_id=:teacherUserId) WHERE tss.schedule_test_id IN  (:scheduleTestIds)

            """,
      nativeQuery = true)
  List<ScheduleTestStudentResponse> getAllStudentsByScheduledIdsAndTeacherId(
      List<Long> scheduleTestIds, long teacherUserId);

  @Query(
      value =
          """
            select * from test_schedule_student tss \
            where tss.student_id = :studentId and \
            tss.schedule_test_id=:scheduleTestId and tss.status='COMPLETED' \
            """,
      nativeQuery = true)
  Optional<ScheduleTestStudent> completedScheduledTest(long studentId, long scheduleTestId);

  @Query(
      """
            select sts from ScheduleTestStudent sts left \
            join fetch sts.scheduleTest st where st.id in (:scheduleTestIds)\
            """)
  List<ScheduleTestStudent> getAllStudentsByScheduledId(List<Long> scheduleTestIds);

  @Query(
      "select count(e) from Exam e where e.endTime is not null and e.scheduleTest=:scheduleTest and e.corrected=false")
  long getExamsNotCorrectedCount(ScheduleTest scheduleTest);

  Optional<ScheduleTestStudent> findByStudentAndUuid(User studentUser, String uuid);

  List<ScheduleTestStudent> findByStatusAndResultProcessingTimeBefore(
      String status, LocalDateTime resultProcessingTime);

  @Query(
      value =
          """
            select * from test_schedule_student where allowed_end_time is not null and
            status in ('STARTED','PENDING') and allowed_end_time < now()""",
      nativeQuery = true)
  List<ScheduleTestStudent> findTestsToBeAutoSubmitted();

  @Query(
      value =
          """
                   select * from test_schedule_student tss where tss.schedule_test_id in (:scheduleTestIds)
            """,
      nativeQuery = true)
  List<ScheduleTestStudent> getAllStudentsByScheduledIds(List<Long> scheduleTestIds);

  @Query(
      value =
          "select * from  test_schedule_student tss  where schedule_test_id = :scheduleTestId and uuid like concat(:uuidPrefix,'%')",
      nativeQuery = true)
  Optional<ScheduleTestStudent> fetchByScheduleTestAndPrefixUuid(
      long scheduleTestId, String uuidPrefix);

  Optional<ScheduleTestStudent> findByScheduleTestAndStudent(
      ScheduleTest scheduleTest, User student);

  Long countByScheduleTestAndStatusIn(ScheduleTest scheduleTest, List<String> status);

  @Query(
      value =
          """
            select tss.*  from test_schedule_student tss
            join test_schedule ts  on ts.id = tss.schedule_test_id
            join test_definitions td on td.id = ts.test_definition_id
            where td.id = :testDefId and tss.student_id = :studentId""",
      nativeQuery = true)
  Optional<ScheduleTestStudent> findByTestDefinitionAndStudentId(long studentId, long testDefId);

  Optional<ScheduleTestStudent> findTopByStudentIdAndStatusAndCreatedAtBeforeOrderByCreatedAtDesc(
      Long studentId, String status, Timestamp createdAt);

  Long countByStudentIdAndStatus(long studentUserId, String status);

  @Query(
      value =
          """
                  SELECT
                    ts.start_date AS startDate,
                    ts.id AS testScheduleId,
                    tss.status AS testStatus,
                    tss.student_id AS studentId,
                    u.first_name AS firstName,
                    u.last_name AS lastName,
                    u.mobile_number AS mobileNumber,
                    u.email as emailId,
                    ps.status AS proctoringStatus
                  FROM users u
                  JOIN test_schedule_student tss ON tss.student_id = u.id
                  left JOIN proctoring_sessions ps ON ps.test_schedule_id = tss.schedule_test_id
                  JOIN test_schedule ts ON ts.id = tss.schedule_test_id
                  WHERE u.organization =:orgSlug
                  """,
      nativeQuery = true)
  List<ScheduleTestStudentAndStatus> findTestScheduleTestsByOrgSlug(String orgSlug);

  @Query(
      value =
          """
            select td.id as testDefinitionId,td.test_name as testName,td.organization as orgSlug ,td.total_marks as totalMarks,td.no_of_questions as totalQuestions,tss.status as status,
            e.id as examId,e.end_time as completedDate,ts.id as scheduleId ,  tss."uuid" as tssUuid, td.category as testCategory, td.grade_slug as  gradeSlug from test_definitions td
             join test_schedule ts on ts.test_definition_id = td.id
             join test_schedule_student tss on tss.schedule_test_id = ts.id
             join exams e on e.schedule_test_id = ts.id
             where td.category in (:testCategories)   and td.organization = :orgSlug and board_slug in (:boards)
             and published_at is not null and tss.student_id = :studentId and td.deleted_at  is null
          """,
      nativeQuery = true)
  List<TestDetailsUser> getStudentAttemptedTestsByCategory(
      String orgSlug, List<String> boards, List<TestCategory> testCategories, long studentId);
}
