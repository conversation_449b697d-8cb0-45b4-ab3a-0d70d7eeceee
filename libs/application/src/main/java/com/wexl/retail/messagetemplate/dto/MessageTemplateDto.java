package com.wexl.retail.messagetemplate.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public class MessageTemplateDto {
  @Builder
  public record Request(
      MessageType type,
      String message,
      @JsonProperty("message_template_category_id") Long messageTemplateCategoryId,
      @JsonProperty("sms_dlt_template_id") String smsDltTemplateId,
      @JsonProperty("whatsapp_template_id") String whatsappTemplateId,
      @JsonProperty("email_template_id") String emailTemplateId) {}

  @Builder
  public record Response(
      Long id,
      MessageType type,
      String message,
      @JsonProperty("message_template_category") String messageTemplateCategory,
      @JsonProperty("sms_dlt_template_id") String smsDltTemplateId,
      @JsonProperty("email_template_id") String emailTemplateId,
      @JsonProperty("whatsapp_template_id") String whatsAppTemplateId) {}

  @Builder
  public record Attributes(@JsonProperty("message_templates") List<Long> messageTemplate) {}
}
