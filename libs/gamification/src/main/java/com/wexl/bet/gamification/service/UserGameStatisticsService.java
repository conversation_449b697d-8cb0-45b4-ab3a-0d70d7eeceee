package com.wexl.bet.gamification.service;

import com.wexl.bet.gamification.dto.EventType;
import com.wexl.bet.gamification.dto.GamificationDto;
import com.wexl.bet.gamification.model.Event;
import com.wexl.bet.gamification.model.LeagueLevel;
import com.wexl.bet.gamification.model.UserGameStatistics;
import com.wexl.bet.gamification.repository.EventsRepository;
import com.wexl.bet.gamification.repository.UserGameStatisticsRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.util.ValidationUtils;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserGameStatisticsService {
  private final UserGameStatisticsRepository userGameStatisticsRepository;
  private final UserRepository userRepository;
  private final EventsRepository eventsRepository;

  private final ValidationUtils validationUtils;
  private final ProfileService profileService;
  private static final int POINTS_FOR_SILVER = 3000;
  private static final int POINTS_FOR_GOLD = 6000;
  private static final int POINTS_FOR_RUBY = 9000;
  private static final int POINTS_FOR_AMETHYST = 12000;
  private static final int POINTS_FOR_EMERALD = 15000;

  @Transactional
  public void addUserPoints(String authUserId, int points) {
    var user = isValidUser(authUserId);
    Optional<UserGameStatistics> byUser = userGameStatisticsRepository.findByUser(user);
    if (byUser.isEmpty()) {
      UserGameStatistics userGameStatistics =
          UserGameStatistics.builder()
              .name(user.getFirstName())
              .currentStreak(1)
              .lastStreakDate(LocalDate.now())
              .totalXp(points)
              .user(user)
              .gemBalance(0)
              .lastWeekXp(0)
              .leagueLevel(LeagueLevel.BRONZE)
              .build();
      userGameStatisticsRepository.save(userGameStatistics);
      return;
    }
    var userData = byUser.get();
    var currentStreak =
        calculateCurrentStreak(userData.getCurrentStreak(), userData.getLastStreakDate());

    userData.setCurrentStreak(currentStreak);
    userData.setLastStreakDate(LocalDate.now());
    userData.setTotalXp(Optional.of(userData.getTotalXp()).orElse(0) + points);
    userData.setLeagueLevel(determineLevelFromPoints(userData.getTotalXp()));
    userData.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    userGameStatisticsRepository.save(userData);
  }

  public void sortUserGameStats(List<GamificationDto.UserGameStatus> userGameStats) {
    userGameStats.sort(Comparator.comparing(GamificationDto.UserGameStatus::totalXp).reversed());
  }

  public int calculateCurrentStreak(int existingStreak, LocalDate lastStreakDate) {
    LocalDate today = LocalDate.now();

    if (lastStreakDate == null || (lastStreakDate.equals(today) && existingStreak == 0)) {
      return 1;
    }

    if (lastStreakDate.equals(today)) {
      return existingStreak;
    }
    LocalDate checkDate = today.minusDays(1);
    while (checkDate.isAfter(lastStreakDate)) {
      DayOfWeek day = checkDate.getDayOfWeek();
      if (day != DayOfWeek.SATURDAY && day != DayOfWeek.SUNDAY) {
        return 0;
      }
      checkDate = checkDate.minusDays(1);
    }
    if (checkDate.equals(lastStreakDate)) {
      return existingStreak + 1;
    }
    return 0;
  }

  public GamificationDto.UserGameStatus getUserGameStats(String authUserId) {
    var user = isValidUser(authUserId);
    Optional<UserGameStatistics> byUser = userGameStatisticsRepository.findByUser(user);
    if (byUser.isEmpty()) {
      return GamificationDto.UserGameStatus.builder()
          .id(0L)
          .currentStreak(0)
          .totalXp(0)
          .lastStreakDate(null)
          .build();
    }
    var userData = byUser.get();
    return GamificationDto.UserGameStatus.builder()
        .id(userData.getId())
        .currentStreak(userData.getCurrentStreak())
        .totalXp(userData.getTotalXp())
        .lastStreakDate(
            (userData.getLastStreakDate() == null || userData.getCurrentStreak() == 0)
                ? null
                : DateTimeUtil.convertIso8601ToEpoch(userData.getLastStreakDate().atStartOfDay()))
        .dates(buildDailyPoints(user.getId()))
        .build();
  }

  private Map<LocalDate, Integer> buildDailyPoints(long userId) {
    var events = eventsRepository.findAllByUserIdAndEventType(userId, EventType.LESSON_COMPLETED);
    LocalDate today = LocalDate.now();
    Map<LocalDate, Integer> dailyPoints = new LinkedHashMap<>();
    for (int i = 0; i < 60; i++) {
      LocalDate date = today.minusDays(i);
      dailyPoints.put(date, 0);
    }

    for (Event event : events) {
      if (event.getCreatedAt() != null) {
        LocalDate eventDate = event.getCreatedAt().toLocalDateTime().toLocalDate();
        if (dailyPoints.containsKey(eventDate)) {
          dailyPoints.put(eventDate, dailyPoints.get(eventDate) + event.getPointsAwarded());
        }
      }
    }

    return dailyPoints;
  }

  public User isValidUser(String authUserId) {
    var optionalUser = userRepository.findByAuthUserId(authUserId);
    if (optionalUser.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
    }
    return optionalUser.get();
  }

  @Transactional
  public List<GamificationDto.LeaguePointsResponse> getLeaguePoints() {
    var eventList = eventsRepository.getUserPoints();

    if (eventList.isEmpty()) {
      return Collections.emptyList();
    }

    return eventList.stream()
        .map(
            event -> {
              var user = validationUtils.getUserById(event.getUserId());
              LeagueLevel level = determineLevelFromPoints(event.getTotalXp());

              return GamificationDto.LeaguePointsResponse.builder()
                  .points(event.getTotalXp())
                  .name(user.getFirstName() + " " + user.getLastName())
                  .rank(event.getRank())
                  .level(level.name())
                  .profileImageUrl(
                      Objects.isNull(user.getProfileImage())
                          ? null
                          : profileService.getProfileImageUrl(user.getProfileImage()))
                  .build();
            })
        .toList();
  }

  public LeagueLevel determineLevelFromPoints(int totalXp) {
    if (totalXp >= POINTS_FOR_EMERALD) return LeagueLevel.EMERALD;
    if (totalXp >= POINTS_FOR_AMETHYST) return LeagueLevel.AMETHYST;
    if (totalXp >= POINTS_FOR_RUBY) return LeagueLevel.RUBY;
    if (totalXp >= POINTS_FOR_GOLD) return LeagueLevel.GOLD;
    if (totalXp >= POINTS_FOR_SILVER) return LeagueLevel.SILVER;
    return LeagueLevel.BRONZE;
  }
}
