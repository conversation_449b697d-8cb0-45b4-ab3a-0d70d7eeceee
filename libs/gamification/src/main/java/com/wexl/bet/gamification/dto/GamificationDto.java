package com.wexl.bet.gamification.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.bet.gamification.model.LeagueLevel;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.Builder;

public record GamificationDto() {

  @Builder
  public record UserGameStatus(
      Long id,
      @JsonProperty("current_streak") Integer currentStreak,
      @JsonProperty("total_xp") Integer totalXp,
      @JsonProperty("last_streak_date") Long lastStreakDate,
      @JsonProperty("daily_points") Map<LocalDate, Integer> dates) {}

  @Builder
  public record LeaguePointsResponse(
      String name,
      Integer points,
      Integer rank,
      String level,
      @JsonProperty("profile_image_url") String profileImageUrl) {}

  @Builder
  public record TopPerformersResponse(
      LeagueLevel leagueLevel, List<LeaguePointsResponse> topPerformers) {}
}
