package com.wexl.bet.gamification.model;

import com.wexl.bet.gamification.dto.EventType;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "events")
public class Event extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @NotNull
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private EventType eventType;

  @Column(name = "points_awarded")
  private Integer pointsAwarded;

  @Column(name = "gems_awarded")
  private Integer gemsAwarded;

  @Type(JsonType.class)
  @Column(name = "event_payload", columnDefinition = "jsonb")
  private String eventPayload;
}
