package com.wexl.bet.gamification.service;

import com.wexl.bet.gamification.dto.EventDetail;
import com.wexl.bet.gamification.dto.EventType;
import com.wexl.bet.gamification.dto.GamificationDto;
import com.wexl.bet.gamification.event.LessonCompletionEvent;
import com.wexl.bet.gamification.model.Event;
import com.wexl.bet.gamification.model.LeagueLevel;
import com.wexl.bet.gamification.model.UserGameStatistics;
import com.wexl.bet.gamification.repository.EventsRepository;
import com.wexl.bet.gamification.repository.UserGameStatisticsRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.User;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.util.ValidationUtils;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PointsService {
  private final ApplicationEventPublisher applicationEventPublisher;
  private final UserGameStatisticsService userGameStatisticsService;
  private final EventsRepository eventListenerRepository;
  private final ValidationUtils validationUtils;
  private final ProfileService profileService;

  private final UserGameStatisticsRepository userGameStatisticsRepository;

  @Scheduled(cron = "0 0 0 * * MON")
  @Transactional
  public void processWeeklyLeague() {
    updateUserRanks();
  }

  private void updateUserRanks() {
    List<UserGameStatistics> allUsers = userGameStatisticsRepository.findAll();
    for (LeagueLevel leagueLevel : LeagueLevel.values()) {
      List<UserGameStatistics> filteredList =
          allUsers.stream()
              .filter(x -> x.getLeagueLevel().equals(leagueLevel))
              .sorted(
                  Comparator.comparing(
                          (UserGameStatistics ugs) -> ugs.getTotalXp() - ugs.getLastWeekXp())
                      .reversed())
              .toList();

      List<UserGameStatistics> thisLeagueTop3 =
          filteredList.subList(0, Math.min(3, filteredList.size()));

      thisLeagueTop3.forEach(
          userGameStatistics -> {
            if (promoteUserLeague(userGameStatistics)) {
              userGameStatistics.setLeagueLevel(promoteUserLeagueLevel(userGameStatistics));
              userGameStatistics.setLastWeekXp(userGameStatistics.getTotalXp());
            }
          });
    }
    for (UserGameStatistics userGameStatistics : allUsers) {
      userGameStatistics.setLastWeekXp(userGameStatistics.getTotalXp());
    }
    userGameStatisticsRepository.saveAll(allUsers);
  }

  private boolean promoteUserLeague(UserGameStatistics userGameStatistics) {
    return (userGameStatistics.getTotalXp() - userGameStatistics.getLastWeekXp()) >= 3000
        && userGameStatistics.getLeagueLevel().ordinal() < LeagueLevel.EMERALD.ordinal();
  }

  private LeagueLevel promoteUserLeagueLevel(UserGameStatistics userGameStatistics) {
    return LeagueLevel.values()[userGameStatistics.getLeagueLevel().ordinal() + 1];
  }

  public void addEvent(String authUserId) {
    Event event = new Event();
    var user = userGameStatisticsService.isValidUser(authUserId);
    event.setEventType(EventType.LESSON_COMPLETED);
    event.setPointsAwarded(100);
    event.setUserId(user.getId());
    eventListenerRepository.save(event);
  }

  public void simulateDailyQuest(String authUserId) {
    EventDetail.EventObject event =
        new EventDetail.EventObject(authUserId, EventType.LESSON_COMPLETED);
    applicationEventPublisher.publishEvent(new LessonCompletionEvent(event));
    applicationEventPublisher.publishEvent(
        new EventDetail.LessonScoreEventObject(authUserId, "lesson1", 85));
    applicationEventPublisher.publishEvent(
        new EventDetail.LessonScoreEventObject(authUserId, "lesson2", 95));
    applicationEventPublisher.publishEvent(
        new EventDetail.LessonScoreEventObject(authUserId, "lesson3", 100));
  }

  @Transactional
  public GamificationDto.TopPerformersResponse getPerformersByLevel(
      String authUserId, LeagueLevel incomingLevel, boolean fetchGlobalPerformers) {
    var learnerUser = validationUtils.isValidUser(authUserId);
    LeagueLevel level =
        incomingLevel == null
            ? userGameStatisticsRepository
                .findByUser(learnerUser)
                .map(UserGameStatistics::getLeagueLevel)
                .orElseThrow(
                    () ->
                        new ApiException(
                            InternalErrorCodes.INVALID_REQUEST, "User game statistics not found"))
            : incomingLevel;
    List<UserGameStatistics> userGameStatistics;
    if (fetchGlobalPerformers) {
      userGameStatistics = userGameStatisticsRepository.findAllByLeagueLevel(level);
    } else {
      userGameStatistics =
          userGameStatisticsRepository.findAllByLeagueLevelAndOrg(
              level.name(), learnerUser.getOrganization());
    }

    AtomicInteger rank = new AtomicInteger(0);
    var topPerformersByLevel =
        userGameStatistics.stream()
            .sorted(
                Comparator.comparingInt(
                        (UserGameStatistics ugs) ->
                            (Objects.nonNull(ugs.getTotalXp()) ? ugs.getTotalXp() : 0)
                                - ugs.getLastWeekXp())
                    .reversed())
            .map(
                gameStatistics -> {
                  User user = gameStatistics.getUser();
                  return GamificationDto.LeaguePointsResponse.builder()
                      .points(gameStatistics.getTotalXp() - gameStatistics.getLastWeekXp())
                      .name(user.getFirstName() + " " + user.getLastName())
                      .rank(rank.incrementAndGet())
                      .level(gameStatistics.getLeagueLevel().name())
                      .profileImageUrl(
                          Objects.isNull(user.getProfileImage())
                              ? null
                              : profileService.getProfileImageUrl(user.getProfileImage()))
                      .build();
                })
            .toList();

    return GamificationDto.TopPerformersResponse.builder()
        .leagueLevel(level)
        .topPerformers(topPerformersByLevel)
        .build();
  }
}
