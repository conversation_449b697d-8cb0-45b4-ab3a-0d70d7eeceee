package com.wexl.bet.gamification.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "quests")
public class Quest extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private String name;

  @Column(nullable = false)
  private String description;

  @Column(name = "xp_reward")
  private Integer xpReward;

  @Column(name = "gem_reward")
  private Integer gemReward;

  @Column(name = "active_start", nullable = false)
  private LocalDateTime activeStart;

  @Column(name = "active_end", nullable = false)
  private LocalDateTime activeEnd;
}
