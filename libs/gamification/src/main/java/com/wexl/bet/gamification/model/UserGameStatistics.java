package com.wexl.bet.gamification.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "user_game_stats")
public class UserGameStatistics extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne
  @JoinColumn(name = "user_id")
  private User user;

  @Column(nullable = false)
  private String name;

  @Column(name = "total_xp")
  private Integer totalXp = 0;

  @Column(name = "gem_balance")
  private Integer gemBalance;

  @Column(name = "current_streak")
  private Integer currentStreak;

  @Column(name = "last_streak_date")
  private LocalDate lastStreakDate;

  @Column(name = "last_week_xp")
  private Integer lastWeekXp = 0;

  @Enumerated(EnumType.STRING)
  @Column(name = "league_level")
  private LeagueLevel leagueLevel = LeagueLevel.BRONZE;
}
