package com.wexl.bet.gamification.controller;

import com.wexl.bet.gamification.dto.GamificationDto;
import com.wexl.bet.gamification.model.LeagueLevel;
import com.wexl.bet.gamification.service.PointsService;
import com.wexl.bet.gamification.service.UserGameStatisticsService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class PointsController {
  private final PointsService pointsService;
  private final UserGameStatisticsService userGameStatisticsService;

  @PostMapping("/orgs/{orgSlug}/users/{authUserId}/points")
  public void triggerLessonCompletedEvent(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    pointsService.addEvent(authUserId);
  }

  public void triggerDailyQuestCompletionEvent(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    pointsService.simulateDailyQuest(authUserId);
  }

  @GetMapping("/users/{authUserId}/game-stats")
  public GamificationDto.UserGameStatus getUserGameStats(@PathVariable String authUserId) {
    return userGameStatisticsService.getUserGameStats(authUserId);
  }

  @GetMapping("/orgs/{orgSlug}/league-points")
  public List<GamificationDto.LeaguePointsResponse> getLeaguePoints() {
    return userGameStatisticsService.getLeaguePoints();
  }

  @GetMapping("/users/{authUserId}/league/top-performers")
  public GamificationDto.TopPerformersResponse getPerformersByLevel(
      @PathVariable String authUserId,
      @RequestParam(required = false) LeagueLevel level,
      @RequestParam(value = "fetchGlobalPerformers", defaultValue = "false")
          boolean fetchGlobalPerformers) {
    return pointsService.getPerformersByLevel(authUserId, level, fetchGlobalPerformers);
  }
}
