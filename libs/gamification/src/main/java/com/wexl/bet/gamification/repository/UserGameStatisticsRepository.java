package com.wexl.bet.gamification.repository;

import com.wexl.bet.gamification.dto.UserGameStatisticsDetails;
import com.wexl.bet.gamification.model.LeagueLevel;
import com.wexl.bet.gamification.model.UserGameStatistics;
import com.wexl.retail.model.User;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserGameStatisticsRepository extends JpaRepository<UserGameStatistics, Long> {
  Optional<UserGameStatistics> findByUser(User user);

  @Modifying
  @Query(
      "UPDATE UserGameStatistics u SET u.totalXp = u.totalXp + :points, u.lastStreakDate = :today,u.currentStreak = :currentStreak"
          + " WHERE u.user = :user")
  @Transactional
  int updateTotalXpByUser(
      @Param("user") User user,
      @Param("points") int points,
      @Param("today") LocalDate today,
      @Param("currentStreak") int currentStreak);

  List<UserGameStatistics> findAllByLeagueLevel(LeagueLevel level);

  @Query(
      value =
          """
                  select coalesce(avg(ugs.total_xp),0) as averageXpPoints ,coalesce(sum(ugs.current_streak) ,0) as totalCurrentStreak ,
                    coalesce(sum(ugs.last_week_xp ) ,0) as lastWeekXpPoints from user_game_stats ugs
                     join users u on u.id = ugs.user_id
                     where u.organization =:orgSlug""",
      nativeQuery = true)
  UserGameStatisticsDetails getUserGameStatisticsDetails(@Param("orgSlug") String orgSlug);

  @Query(
      value =
          """
          select ugs.* from user_game_stats ugs
          join users  u on u.id = ugs.user_id
          where u.deleted_at  is null and
          u.organization in(:orgSlugs)""",
      nativeQuery = true)
  List<UserGameStatistics> getUserGameStatsByOrgs(List<String> orgSlugs);

  @Query(
      value =
          """
                  select ugs.* from user_game_stats ugs
                  join users u on u.id = ugs.user_id
                  where  ugs.league_level = :level  and u.organization=:organization
                  and u.deleted_at is null""",
      nativeQuery = true)
  List<UserGameStatistics> findAllByLeagueLevelAndOrg(String level, String organization);
}
