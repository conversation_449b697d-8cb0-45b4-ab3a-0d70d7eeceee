package com.wexl.speech.processor.speechace;

import com.wexl.ai.EnglishTutor;
import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Service
@RequiredArgsConstructor
public class AiService {

  private final EnglishTutor englishTutor;
  private final SpringTemplateEngine templateEngine;

  public ExamAnalysis.AnswerEvaluationResponse answerAnalysis(
      SpeechEvaluation.AnswerEvaluationRequest request) {
    var userPrompt = buildPrompt(request);
    return englishTutor.speechAnswerAnalysis(userPrompt);
  }

  private String buildPrompt(SpeechEvaluation.AnswerEvaluationRequest request) {
    var context = new Context();
    context.setVariable("str", request);
    return templateEngine.process("prompts/speech-evaluation-prompt", context);
  }
}
