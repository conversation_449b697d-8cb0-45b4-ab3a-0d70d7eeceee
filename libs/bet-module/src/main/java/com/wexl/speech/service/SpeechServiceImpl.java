package com.wexl.speech.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.speech.PronunciationAssessmentService;
import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechTaskResponse;
import com.wexl.speech.domain.SpeechTask;
import com.wexl.speech.repository.SpeechRepository;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
@Order(1)
public class SpeechServiceImpl implements SpeechService {

  private final SpeechRepository speechRepository;
  private final PronunciationAssessmentService pronunciationAssessmentService;

  @Override
  public SpeechTaskResponse pronunciationAssessment(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech) {
    final SpeechResponse speechResponse =
        evaluateSpeech(text, audioUrl, reference, isImpromptuSpeech);
    var speechTask = saveSpeechResponse(reference, speechResponse);

    return SpeechTaskResponse.builder()
        .speechResponse(speechResponse)
        .speechTaskId(Objects.nonNull(speechTask.getId()) ? speechTask.getId() : null)
        .build();
  }

  @Override
  public SpeechResponse pronunciationAssessment(String reference) {
    final Optional<SpeechTask> possibleSpeechTask =
        speechRepository.findAllBySpeechRefOrderByCreatedAtDesc(reference).stream().findFirst();
    return possibleSpeechTask.map(this::constructSpeechResponse).orElse(null);
  }

  @Override
  public SpeechResponse migrateSpeechTask(String speechRef, long speechTaskId) {
    final Optional<SpeechTask> possibleSpeechTask = speechRepository.findById(speechTaskId);
    return possibleSpeechTask
        .map(
            speechTask -> {
              speechTask.setSpeechRef(speechRef);
              speechTask.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
              return constructSpeechResponse(speechRepository.save(speechTask));
            })
        .orElse(null);
  }

  private SpeechResponse evaluateSpeech(
      String text, String audioUrl, String reference, Boolean isImpromptuSpeech) {
    return pronunciationAssessmentService.pronunciationAssessment(
        text, audioUrl, reference, isImpromptuSpeech);
  }

  private SpeechTask saveSpeechResponse(String reference, SpeechResponse speechResponse) {
    if (speechResponse == null) {
      return null;
    }

    try {
      ObjectMapper objectMapper = new ObjectMapper();
      String json = objectMapper.writeValueAsString(speechResponse);

      SpeechTask speechTask = SpeechTask.builder().speechRef(reference).response(json).build();

      return speechRepository.save(speechTask);

    } catch (Exception ex) {
      log.error("Unable to serialize the speech response for reference {}", reference);
      return null;
    }
  }

  private SpeechResponse constructSpeechResponse(SpeechTask speechTask) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      String azureSpeechResponse = speechTask.getResponse();
      return objectMapper.readValue(azureSpeechResponse, SpeechResponse.class);
    } catch (Exception ex) {
      log.info("Unable to serialize the speech task for task id {}", speechTask.getId());
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.speechGenericServerError");
    }
  }
}
