package com.wexl.betsignuplogin.controller;

import com.wexl.betsignuplogin.dto.BetStudentSignUpDto;
import com.wexl.betsignuplogin.dto.BetStudentSignUpDto.LoginOAuthRequest;
import com.wexl.betsignuplogin.dto.BetUserPasswordDto;
import com.wexl.betsignuplogin.services.BetServices;
import com.wexl.retail.auth.dto.LoginRequest;
import com.wexl.retail.auth.dto.LoginResponse;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/public/bet-exams")
public class BetController {
  private final BetServices betServices;

  @PostMapping("/registration")
  public LoginResponse betStudentSignUp(
      @Valid @RequestBody BetStudentSignUpDto.Request request,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    return betServices.betStudentSignUp(request, userAgent);
  }

  @PostMapping("/login-oauth2")
  public LoginResponse loginAndRegister(@Valid @RequestBody LoginOAuthRequest loginOAuthRequest) {
    return betServices.loginOAuth(loginOAuthRequest);
  }

  @GetMapping("/activation-links/{activationKey}")
  public BetStudentSignUpDto.ActivationResponse activationLink(@PathVariable String activationKey) {
    return betServices.activationLink(activationKey);
  }

  @PostMapping("/login")
  public LoginResponse login(
      @Valid @RequestBody LoginRequest loginRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    return betServices.login(loginRequest, userAgent);
  }

  @PutMapping("/forgot-password")
  public BetUserPasswordDto.PasswordResetResponse forgotPassword(
      @Valid @RequestBody BetStudentSignUpDto.ForgotPasswordRequest request) {
    return betServices.forgotPassword(request);
  }

  @PutMapping("/reset-password/{resetLink}")
  public BetUserPasswordDto.PasswordResetResponse resetPassword(
      @PathVariable String resetLink,
      @Valid @RequestBody BetStudentSignUpDto.ResetPasswordRequest request) {
    return betServices.resetPassword(request, resetLink);
  }

  @IsOrgAdmin
  @GetMapping("/orgs/{orgSlug}/bet-results")
  public List<BetStudentSignUpDto.ExamResultResponse> getExamResults(@PathVariable String orgSlug) {
    return betServices.getExamResults(orgSlug);
  }
}
