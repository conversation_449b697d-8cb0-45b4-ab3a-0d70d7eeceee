package com.wexl.betsignuplogin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Gender;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

public record BetStudentSignUpDto() {

  @Builder
  public record Request(@NotNull String email, @NotNull String password, String appContext) {}

  @Builder
  public record ForgotPasswordRequest(@NotNull String email) {}

  @Builder
  public record ResetPasswordRequest(@NotNull String password) {}

  @Builder
  public record ActivationResponse(String message) {}

  @Builder
  public record UserDetailsResponse(
      String email,
      @JsonProperty("mobile_number") String mobileNumber,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("organization_name") String orgName,
      Gender gender,
      String country,
      String address,
      String city,
      String state,
      @JsonProperty("cart_id") Long cartId,
      @JsonProperty("profile_image_url") String profileImageUrl,
      @JsonProperty("cart_item_count") Long cartItemCount,
      @JsonProperty("verification_status") boolean verificationStatus,
      @JsonProperty("proctoring_toggle") boolean proctoringToggle,
      @JsonProperty("proctoring_enabled") boolean proctoringEnabled,
      @JsonProperty("bet_corporate_Schedules") BetScheduleDetails betCorporateSchedules) {}

  @Builder
  public record BetScheduleDetails(
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("test_schedule_id") Long scheduleId,
      @JsonProperty("tss_uuid") String tssUuid) {}

  public enum OAuthType {
    GOOGLE,
    APPLE
  }

  public record LoginOAuthRequest(@NotNull @Valid OAuthType type, @NotNull String jwt) {}

  @Builder
  public record OAuth2Request(
      String name, String givenName, String familyName, String email, boolean isEmailVerified) {}

  @Builder
  public record ExamResultResponse(
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("email_id") String emailId,
      @JsonProperty("exam_date") long examDate,
      @JsonProperty("mobile_number") String mobileNumber,
      @JsonProperty("proctor_status") ProctoringStatus proctorStatus,
      @JsonProperty("test_status") TestStudentStatus testStatus,
      @JsonProperty("orgSlug") String orgSlug,
      @JsonProperty("student_auth_id") String studentAuthId,
      @JsonProperty("schedule_test_id") Long scheduleTestId) {}
}
