package com.wexl.writing.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.ai.EnglishTutor;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.writing.dto.WritingTaskDto;
import com.wexl.writing.model.WritingTask;
import com.wexl.writing.repository.WritingTaskRepository;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@RequiredArgsConstructor
@Slf4j
public class WritingTaskService {
  private final WritingTaskRepository writingTaskRepository;
  private final RestTemplate restTemplate;
  private final TemplateEngine templateEngine;
  private final EnglishTutor englishTutor;

  public WritingTaskDto.StudentAnalysisReportResponse getWritingTaskAnalysis(String reference) {
    var writingTask = writingTaskRepository.findByWritingRef(reference);
    return writingTask.map(this::constructWritingResponse).orElse(null);
  }

  private WritingTaskDto.StudentAnalysisReportResponse constructWritingResponse(
      WritingTask writingTask) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      String azureSpeechResponse = writingTask.getResponse();
      return objectMapper.readValue(
          azureSpeechResponse, WritingTaskDto.StudentAnalysisReportResponse.class);
    } catch (Exception ex) {
      log.info("Unable to serialize the speech task for task id {}", writingTask.getId());
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.speechGenericServerError");
    }
  }

  public void saveWritingTaskAnalysis(
      String reference, WritingTaskDto.StudentAnalysisReportResponse response) {
    if (response == null) {
      log.error("Received null response for writing task with reference {}", reference);
      return;
    }

    try {
      ObjectMapper objectMapper = new ObjectMapper();
      String json = objectMapper.writeValueAsString(response);

      WritingTask speechTask = WritingTask.builder().writingRef(reference).response(json).build();

      writingTaskRepository.save(speechTask);

    } catch (Exception ex) {
      log.error("Unable to serialize the speech response for reference {}", reference);
    }
  }

  public WritingTaskDto.StudentAnalysisReportResponse grammarAnalysis(
      WritingTaskDto.QuestionAnswerRequest request) {

    final WritingTaskDto.Response grammarCorrection = getGrammarCorrection(request.studentAnswer());
    if (grammarCorrection != null && grammarCorrection.response() != null) {

      var grammar = constructGrammarAnalysis(grammarCorrection, "grammar");
      var sentenceAnalysis = constructSentenceAnalysis(grammarCorrection);
      var answerAnnotated = annotateContent(request.studentAnswer(), grammarCorrection);
      WritingTaskDto.KeywordAnalysis keywordAnalysis =
          constructKeywordAnalysis(request.studentAnswer());
      var userPrompt = constructUserPrompt(request);
      var analysis = englishTutor.performAnalysis(userPrompt);

      return WritingTaskDto.StudentAnalysisReportResponse.builder()
          .answer(answerAnnotated)
          .marks(analysis.response().getFirst().marksAwarded())
          .aiSummary(analysis.response().getFirst().reasonForMarksAwarded())
          .showGrammarAnalysis(grammar.total() > 0)
          .grammar(grammar)
          .punctuation(constructGrammarAnalysis(grammarCorrection, "punctuation"))
          .spelling(constructGrammarAnalysis(grammarCorrection, "spelling"))
          .stats(constructStudentAnalysisStatsUnit(sentenceAnalysis))
          .keywords(constructKeywords(keywordAnalysis))
          .build();
    }
    return WritingTaskDto.StudentAnalysisReportResponse.builder().build();
  }

  private WritingTaskDto.StudentAnalysisStatsUnit constructStudentAnalysisStatsUnit(
      WritingTaskDto.SentenceAnalysis sentenceAnalysis) {
    if (sentenceAnalysis == null) {
      return null;
    }
    NumberFormat format = new DecimalFormat("0.#");
    return WritingTaskDto.StudentAnalysisStatsUnit.builder()
        .words(sentenceAnalysis.words())
        .clearTextLength(sentenceAnalysis.clearLength())
        .readabilityScore(format.format(sentenceAnalysis.readingEase()))
        .textLength(sentenceAnalysis.length())
        .build();
  }

  private List<WritingTaskDto.StudentAnalysisKeyword> constructKeywords(
      WritingTaskDto.KeywordAnalysis keywordAnalysis) {
    if (keywordAnalysis == null) {
      return new ArrayList<>();
    }
    final String studentKeywords = keywordAnalysis.keywords();
    final String[] studentKeywordStringArray = fromJson(studentKeywords, String[].class);
    List<WritingTaskDto.StudentAnalysisKeyword> finalList = new ArrayList<>();
    for (String s : studentKeywordStringArray) {
      finalList.add(
          WritingTaskDto.StudentAnalysisKeyword.builder().word(s).available(true).build());
    }
    return finalList;
  }

  @Nullable
  public WritingTaskDto.Response getGrammarCorrection(String text) {
    if (StringUtils.isBlank(text)) {
      return null;
    }
    final WritingTaskDto.Request request =
        WritingTaskDto.Request.builder()
            .ai(true)
            .text(text)
            .language("en-US")
            .key("pxyKYOBv8BC8ZGrF")
            .build();
    return restTemplate.postForObject(
        "https://sg.api.textgears.com/analyze", request, WritingTaskDto.Response.class);
  }

  private static String getRecommendation(WritingTaskDto.Error error) {
    if (error.better().isEmpty()) {
      return "No Recommendation";
    }
    return error.better().getFirst();
  }

  @Nullable
  public WritingTaskDto.KeywordAnalysis constructKeywordAnalysis(String explanation) {
    if (StringUtils.isBlank(explanation)) {
      return null;
    }
    try {
      final WritingTaskDto.SummarizationResponse textSummaryResponse = getTextSummary(explanation);
      final String keywords = toJson(textSummaryResponse.response().keywords());
      final String summary = toJson(textSummaryResponse.response().summary());
      final String highlights = toJson(textSummaryResponse.response().highlight());

      return WritingTaskDto.KeywordAnalysis.builder()
          .keywords(keywords)
          .summary(summary)
          .highlights(highlights)
          .build();
    } catch (Exception ex) {
      log.error("Unable to retrieve keywords for explanation {}", explanation);
    }
    return null;
  }

  @Nullable
  private WritingTaskDto.SentenceAnalysis constructSentenceAnalysis(
      WritingTaskDto.Response grammarCorrection) {
    if (grammarCorrection == null
        || grammarCorrection.response() == null
        || grammarCorrection.response().stats() == null) {
      return null;
    }
    final WritingTaskDto.Stats stats = grammarCorrection.response().stats();
    return WritingTaskDto.SentenceAnalysis.builder()
        .readingEase(BigDecimal.valueOf(stats.fleschKincaid().readingEase()))
        .grade(stats.fleschKincaid().grade())
        .interpretation(stats.fleschKincaid().interpretation())
        .colemanLiau(stats.colemanLiau())
        .gunningFog(BigDecimal.valueOf(stats.gunningFog()))
        .smog(stats.smog())
        .length(stats.counter().length())
        .clearLength(stats.counter().clearLength())
        .words(stats.counter().words())
        .sentences(stats.counter().sentences())
        .build();
  }

  private Set<WritingTaskDto.GrammarAnalysis> constructGrammarAnalysis(
      WritingTaskDto.Response grammarCorrection) {
    if (grammarCorrection == null) {
      return new HashSet<>();
    }
    return grammarCorrection.response().grammar().errors().stream()
        .map(
            error ->
                WritingTaskDto.GrammarAnalysis.builder()
                    .answerOffset(error.offset())
                    .length(error.length())
                    .description(error.description().en())
                    .bad(error.bad())
                    .better(getRecommendation(error))
                    .type(error.type())
                    .build())
        .collect(Collectors.toCollection(LinkedHashSet::new));
  }

  public String annotateContent(String content, WritingTaskDto.Response response) {
    if (response == null
        || response.response() == null
        || response.response().grammar() == null
        || response.response().grammar().errors() == null) {
      return content;
    }
    final List<WritingTaskDto.Error> errors = response.response().grammar().errors();
    for (int i = errors.size() - 1; i >= 0; i--) {
      final WritingTaskDto.Error error = errors.get(i);
      content = markSeparatorAtOffset(content, error.offset(), error.length());
    }
    return content;
  }

  public String markSeparatorAtOffset(String content, int offset, int length) {
    return content.substring(0, offset)
        + "<b><u>"
        + content.substring(offset, offset + length)
        + "</b></u>"
        + content.substring(offset + length);
  }

  public WritingTaskDto.SummarizationResponse getTextSummary(String text) {
    final WritingTaskDto.SummarizationRequest request =
        WritingTaskDto.SummarizationRequest.builder()
            .key("pxyKYOBv8BC8ZGrF")
            .text(text)
            .language("en-US")
            .maxSentences(3)
            .build();
    return restTemplate.postForObject(
        "https://sg.api.textgears.com/summarize",
        request,
        WritingTaskDto.SummarizationResponse.class);
  }

  public <T> String toJson(T body) {
    ObjectMapper mapper = new ObjectMapper();
    try {
      return mapper.writeValueAsString(body);
    } catch (Exception e) {
      log.error("Error converting to json", e);
    }
    return null;
  }

  private WritingTaskDto.StudentAnalysisUnit constructGrammarAnalysis(
      WritingTaskDto.Response grammarCorrection, String type) {
    final var grammarAnalyses =
        grammarCorrection.response().grammar().errors().stream()
            .filter(g -> type.equals(g.type()))
            .toList();
    final List<WritingTaskDto.StudentAnalysisUnitGrammar> list =
        grammarAnalyses.stream()
            .map(
                e ->
                    WritingTaskDto.StudentAnalysisUnitGrammar.builder()
                        .bad(e.bad())
                        .better(e.better().isEmpty() ? "No Recommendation" : e.better().getFirst())
                        .description(e.description().en())
                        .build())
            .toList();
    return WritingTaskDto.StudentAnalysisUnit.builder().total(list.size()).errors(list).build();
  }

  public <T> T fromJson(String json, Class<T> clazz) {
    if (StringUtils.isBlank(json)) {
      return null;
    }
    ObjectMapper mapper = new ObjectMapper();
    try {
      return mapper.readValue(json, clazz);
    } catch (Exception e) {
      log.error("Error converting from json for class of type " + clazz, e);
    }
    return null;
  }

  private String constructUserPrompt(WritingTaskDto.QuestionAnswerRequest request) {
    var context = new Context();
    context.setVariable("answer", request);
    return templateEngine.process("prompts/evaluation-answer-evaluation-prompt", context);
  }
}
