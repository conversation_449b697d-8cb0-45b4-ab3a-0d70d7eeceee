package com.wexl.writing.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;

public record WritingTaskDto() {

  @Builder
  public record StudentAnalysisReportResponse(
      @JsonProperty("annotated_answer") String answer,
      @JsonProperty("marks") float marks,
      @JsonProperty("ai_summary") String aiSummary,
      @JsonProperty("show_grammar_analysis") boolean showGrammarAnalysis,
      StudentAnalysisUnit grammar,
      StudentAnalysisUnit spelling,
      StudentAnalysisUnit punctuation,
      StudentAnalysisStatsUnit stats,
      List<StudentAnalysisKeyword> keywords) {}

  @Builder
  public record StudentAnalysisUnit(int total, List<StudentAnalysisUnitGrammar> errors) {}

  @Builder
  public record StudentAnalysisUnitGrammar(String bad, String better, String description) {}

  @Builder
  public record StudentAnalysisStatsUnit(
      @JsonProperty("readability_score") String readabilityScore,
      int words,
      @JsonProperty("text_length") int textLength,
      @JsonProperty("clear_text_length") int clearTextLength) {}

  @Builder
  public record StudentAnalysisKeyword(String word, boolean available) {}

  @Builder
  public record Request(boolean ai, String text, String key, String language) {}

  @Builder
  public record Response(boolean status, TextGearResponse response) {}

  public record TextGearResponse(Grammar grammar, Stats stats) {}

  public record Grammar(boolean result, List<Error> errors) {}

  public record Error(
      int offset,
      int length,
      Description description,
      String bad,
      List<String> better,
      String type) {}

  public record Description(String en) {}

  public record Stats(
      FleschKincaid fleschKincaid,
      Float gunningFog,
      Integer colemanLiau,
      @JsonProperty("SMOG") Integer smog,
      @JsonProperty("counters") Counter counter) {}

  public record FleschKincaid(Float readingEase, String grade, String interpretation) {}

  public record Counter(int length, int clearLength, int words, int sentences) {}

  @Builder
  public record KeywordAnalysis(String keywords, String summary, String highlights) {}

  @Builder
  public record SentenceAnalysis(
      BigDecimal readingEase,
      String grade,
      String interpretation,
      BigDecimal gunningFog,
      Integer colemanLiau,
      Integer smog,
      Integer length,
      Integer clearLength,
      Integer words,
      Integer sentences) {}

  @Builder
  public record GrammarAnalysis(
      @JsonProperty("answer_offset") int answerOffset,
      int length,
      String description,
      String bad,
      String better,
      String type) {}

  @Builder
  public record SummarizationRequest(
      String key, String text, String language, @JsonProperty("max_sentences") int maxSentences) {}

  public record SummarizationResponse(TextGearSummarizationResponse response) {}

  public record TextGearSummarizationResponse(
      List<String> keywords, List<String> highlight, List<String> summary) {}

  @Builder
  public record QuestionAnswerRequest(
      Long questionNumber,
      int marks,
      String questionText,
      String actualAnswer,
      String studentAnswer) {}
}
