package com.wexl.sections.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.sections.models.BetStatus;
import java.util.List;
import lombok.Builder;

public record BetSectionDto() {

  @Builder
  public record BetSectionInstResponse(
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("section_inst_id") Long sectionInstId,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("out_come") String outCome,
      @JsonProperty("bet_section_category_id") Long betSectionCategoryId,
      @JsonProperty("total_units_count") Long totalUnitsCount,
      @JsonProperty("pending_units_count") Long pendingUnitsCount,
      @JsonProperty("completed_units_count") Long completedUnitsCount,
      String title,
      String description,
      @JsonProperty("image_path") String imagePath,
      @JsonProperty("bet_status") BetStatus betStatus) {}

  @Builder
  public record BetSectionUnitResponse(
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("section_unit_id") Long sectionUnitId,
      @JsonProperty("section_unit_inst_id") Long sectionUnitInstId,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("unit_name") String unitName,
      @JsonProperty("unit_category_slug") String unitCategorySlug,
      @JsonProperty("unit_category_name") String unitCategoryName,
      @JsonProperty("unit_category") BetUnitCategory betUnitCategory,
      @JsonProperty("is_latest") Boolean isLatest,
      String description,
      @JsonProperty("bet_status") BetStatus betStatus) {}

  @Builder
  public record BetSectionUnitLessonsResponse(
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("section_unit_id") Long sectionUnitId,
      @JsonProperty("section_unit_lesson_id") Long sectionUnitLessonId,
      @JsonProperty("total_lessons_count") Long totalLessonsCount,
      @JsonProperty("pending_lessons_count") Long pendingLessonsCount,
      @JsonProperty("completed_lessons_count") Long completedLessonsCount,
      @JsonProperty("lesson_status") BetStatus lessonStatus,
      @JsonProperty("section_unit_lesson_insts")
          List<BetSectionUnitLessonInstResponse> sectionUnitLessonInsts,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("lesson_name") String lessonName,
      @JsonProperty("is_latest") Boolean isLatest,
      @JsonProperty("unit_category") BetUnitCategory betUnitCategory,
      @JsonProperty("test_definition_id") Long testDefinitionId) {}

  @Builder
  public record BetSectionUnitLessonInstResponse(
      @JsonProperty("test_definition_section_id") Long testDefinitionSectionId,
      @JsonProperty("test_definition_section_name") String sectionName,
      @JsonProperty("lesson_inst_id") Long lessonInstId,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("bet_status") BetStatus betStatus,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("is_latest") Boolean isLatest,
      @JsonProperty("lesson_attempt_count") Long lessonAttemptCount) {}

  @Builder
  public record BetAnswerResponse(
      @JsonProperty("answer") Long sectionId,
      @JsonProperty("section_unit_id") Long sectionUnitId,
      @JsonProperty("instructions") String instructions,
      @JsonProperty("section_unit_lesson_id") Long sectionUnitLessonId,
      @JsonProperty("section_unit_lesson_inst_id") Long sectionUnitLessonInstId,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("lesson_name") String lessonName,
      @JsonProperty("bet_status") BetStatus betStatus) {}

  @Builder
  public record BetSectionCategoriesResponse(@JsonProperty("id") Long categoryId, String name) {}

  @Builder
  public record InitializeRequest(
      @JsonProperty("section_category_id") Long sectionCategoryId,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("wexl_subject_slug") String wexlSubjectSlug,
      @JsonProperty("wexl_subject_name") String wexlSubjectName) {}

  @Builder
  public record UnitCategories(
      String slug,
      String name,
      String title,
      Integer orderId,
      @JsonProperty("bet_status") BetStatus betStatus,
      @JsonProperty("section_unit_lesson_id") Long sectionUnitLessonId,
      @JsonProperty("section_unit_id") Long sectionUnitId) {}

  @Builder
  public record UploadFileResponse(String path, String url, String previewUrl) {}

  @Builder
  public record TestDefinitionRequest(@JsonProperty("board_slug") String boardSlug) {}

  @Builder
  public record AssignLevelTestResponse(
      @JsonProperty("section_unit_lesson_inst_id") Long sectionUnitLessonInstId) {}

  @Builder
  public record UnitAttributes(String title, String description, CourseDetails courseDetails) {}

  @Builder
  public record CourseDetails(
      @JsonProperty("skill_focus") String skillFocus,
      String level,
      @JsonProperty("audio_units") String audioUnits,
      String practice,
      @JsonProperty("estimated_time") String estimatedTime,
      String gamified,
      @JsonProperty("accessible_on") String accessibleOn,
      @JsonProperty("last_updated") Long lastUpdated) {}

  @Builder
  public record SubmitResponse(@JsonProperty("exam_id") Long examId, BetStatus betStatus) {}

  @Builder
  public record DashBoardWeekResult(Long date, Integer average) {}

  @Builder
  public record CourseLevelResponse(
      @JsonProperty("course_level") String courseLevel,
      @JsonProperty("bet_section_unit_inst_id") Long betSectionUnitInstId,
      @JsonProperty("course_name") String courseName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("bet_section_id") Long betSectionId,
      @JsonProperty("bet_section_inst_id") Long betSectionInstId,
      @JsonProperty("course_level_description") String courseLevelDescription) {}

  @Builder
  public record BetSectionUnitLessonReadingMaterial(String id, String markdown, String title) {}
}
