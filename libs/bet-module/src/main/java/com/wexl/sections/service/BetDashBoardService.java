package com.wexl.sections.service;

import static com.wexl.betcorporate.BetCorporateService.BET_CORP;
import static com.wexl.betcorporate.BetCorporateService.WEXL_INTERNAL;
import static com.wexl.sections.models.BetStatus.UNLOCKED;
import static com.wexl.sections.service.BetSectionService.EBC_BOARD;

import com.wexl.bet.gamification.dto.GamificationDto;
import com.wexl.bet.gamification.service.PointsService;
import com.wexl.bet.gamification.service.UserGameStatisticsService;
import com.wexl.metricshandler.DashBoardDto;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.sections.dto.BetSectionDto;
import com.wexl.sections.dto.GradeUserDto;
import com.wexl.sections.dto.LatestAttemptedLevelResult;
import com.wexl.sections.models.BetSectionInst;
import com.wexl.sections.models.BetSectionUnitInst;
import com.wexl.sections.models.BetStatus;
import com.wexl.sections.repository.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BetDashBoardService {

  private final StudentRepository studentRepository;

  private final BetSectionService betSectionService;

  private final TestDefinitionRepository testDefinitionRepository;

  private final BetSectionUnitLessonsInstRepository betSectionUnitLessonsInstRepository;

  private final BetSectionUnitLessonRepository betSectionUnitLessonsRepository;

  private final BetSectionUnitRepository betSectionUnitRepository;

  private final BetSectionUnitInstRepository betSectionUnitInstRepository;

  private final AuthService authService;

  private final BetSectionRepository betSectionRepository;

  private static final List<String> SKILL_ORDER =
      Arrays.asList("beg", "ele", "inter", "upint", "adv", "pro");

  private final BetSectionInstRepository betSectionInstRepository;

  private final ScheduleTestStudentRepository testStudentRepository;

  private final UserGameStatisticsService userGameStatisticsService;

  private final PointsService pointsService;

  public List<GenericMetricResponse> getAdminDashboard(String org) {

    var testCount =
        testDefinitionRepository
            .countByOrganizationAndTestNameLikeAndPublishedAtIsNotNullAndDeletedAtIsNull(
                WEXL_INTERNAL, BET_CORP);

    Map<String, Object> summary = new HashMap<>();
    summary.put("total_test_count", testCount);
    summary.put("learners_count", studentRepository.getStudentCountByOrg(org));
    summary.put("total_courses", 4);
    summary.put("total_level", 24);

    var students = studentRepository.findByOrgSlug(org);

    var studentAuthUserIds =
        students.stream().map(Student::getUserInfo).map(User::getAuthUserId).toList();

    Map<String, Object> data = new HashMap<>();

    var board = betSectionService.getBoardCurriculum(Constants.WEXL_INTERNAL, EBC_BOARD);
    var mappedStudentsToBestGrade =
        mapStudentsToBestGrade(betSectionUnitRepository.getUserIdAndGradeSlug(studentAuthUserIds));
    board
        .getGrades()
        .forEach(
            grade ->
                summary.put(
                    grade.getName(),
                    Collections.frequency(mappedStudentsToBestGrade.values(), grade.getSlug())));
    summary.put(
        "not_started",
        (Long) summary.get("learners_count") - mappedStudentsToBestGrade.keySet().size());
    var localDate = LocalDateTime.now();

    List<BetSectionDto.DashBoardWeekResult> dashBoardWeekResults = new ArrayList<>();
    IntStream.range(0, 7)
        .forEach(
            day -> {
              var date = localDate.minusDays(day);
              var activeUserCount =
                  betSectionUnitLessonsInstRepository.getActiveUserCountByOrgAndDate(
                      org, date.toLocalDate().toString());
              dashBoardWeekResults.add(
                  BetSectionDto.DashBoardWeekResult.builder()
                      .date(DateTimeUtil.convertIso8601ToEpoch(date))
                      .average((activeUserCount.size()))
                      .build());
            });
    data.put("daily_results", dashBoardWeekResults);
    return List.of(GenericMetricResponse.builder().summary(summary).data(data).build());
  }

  private HashMap<String, String> mapStudentsToBestGrade(List<GradeUserDto> gradeUserDtos) {
    HashMap<String, String> resultMap = new HashMap<>();
    gradeUserDtos.forEach(
        gradeUserDto -> {
          if (resultMap.containsKey(gradeUserDto.getUserId())) {
            var current = resultMap.get(gradeUserDto.getUserId());
            var incoming = gradeUserDto.getGradeSlug();
            resultMap.put(
                gradeUserDto.getUserId(),
                SKILL_ORDER.indexOf(incoming) > SKILL_ORDER.indexOf(current) ? incoming : current);
          } else {
            resultMap.put(gradeUserDto.getUserId(), gradeUserDto.getGradeSlug());
          }
        });
    return resultMap;
  }

  public List<GenericMetricResponse> getEmployeeDashBoard() {
    var user = authService.getStudentDetails();
    Map<String, Object> summary = new HashMap<>();

    GamificationDto.UserGameStatus gameStats =
        userGameStatisticsService.getUserGameStats(user.getAuthUserId());
    var userLevel = userGameStatisticsService.determineLevelFromPoints(gameStats.totalXp());

    summary.put("user_name", user.getFirstName());
    summary.put("level", userLevel);
    summary.put(
        "description",
        "You're now at the "
            + userLevel
            + " Level. Complete these key tasks to level up your English proficiency.");
    summary.put("listening_feedback", "accelerate your listening skills with targeted practice.");
    summary.put("reading_feedback", "Strengthen Comprehensive with a reading test");
    summary.put("speaking_feedback", "Boost fluency with speaking practice sessions.");
    summary.put(
        "writing_feedback",
        "Enhance your writing skills with our interactive lessons and practice sessions.");
    var unitLessonInsts =
        betSectionUnitLessonsInstRepository.findAllByAndUserId(user.getAuthUserId());

    summary.put(
        "completed_lessons",
        unitLessonInsts.stream()
            .filter(bsl -> BetStatus.COMPLETED.equals(bsl.getBetStatus()))
            .count());
    summary.put(
        "lessons_in_progress",
        unitLessonInsts.stream()
            .filter(bsl -> BetStatus.IN_PROGRESS.equals(bsl.getBetStatus()))
            .count());
    summary.put("completed_practices", "3 Practices");
    summary.put("levels_reached", "3 Levels");
    summary.put("study_days", "15 Lessons");
    summary.put(
        "certificates_earned",
        testStudentRepository.countByStudentIdAndStatus(
            user.getId(), TestStudentStatus.COMPLETED.name()));

    summary.put("total_xp", gameStats.totalXp());

    var board = betSectionService.getBoardCurriculum(WEXL_INTERNAL, EBC_BOARD);
    var grades =
        board.getGrades().stream()
            .sorted(Comparator.comparing(Grade::getOrderId).reversed())
            .toList();

    Map<String, Object> data = new HashMap<>();

    data.put("course_title", "ENGLISH PROFICIENCY COURSE");
    data.put(
        "course_description",
        "Enhance your Business Communication Skills with FluentEdge Guided Learning.");
    var levelResult =
        betSectionUnitLessonsInstRepository.getLastestAttemptedLevel(user.getAuthUserId());
    data.put(
        "course_level",
        BetSectionDto.CourseLevelResponse.builder()
            .courseLevel(
                "%s Corporate Communicator"
                    .formatted(
                        massageLevelName(
                            levelResult
                                .map(LatestAttemptedLevelResult::getGradeSlug)
                                .orElse("Intermediate"))))
            .betSectionId(levelResult.map(LatestAttemptedLevelResult::getBetSectionId).orElse(null))
            .betSectionInstId(
                levelResult.map(LatestAttemptedLevelResult::getBetSectionInstId).orElse(null))
            .courseName(levelResult.map(LatestAttemptedLevelResult::getCourseName).orElse(null))
            .gradeName(levelResult.map(LatestAttemptedLevelResult::getGradeName).orElse(null))
            .gradeSlug(levelResult.map(LatestAttemptedLevelResult::getGradeSlug).orElse(null))
            .courseLevelDescription(
                buildCourseLevelDescription(grades, user.getAuthUserId(), levelResult))
            .build());
    data.put("upcoming_course", "More Skill Focused: FluentEdge English Skill Booster");
    data.put(
        "upcoming_course_description",
        "Enhance your Vocabulary,Grammar Pronunciation And Real-Life communication With Engaging Lessons And personalized Learning Paths.");
    data.put("upcoming_course_lessons", "25 lessons");
    data.put("upcoming_course_timings", "5 Hours");

    return List.of(GenericMetricResponse.builder().summary(summary).data(data).build());
  }

  private String buildCourseLevelDescription(
      List<Grade> grades, String authUserId, Optional<LatestAttemptedLevelResult> courseLevel) {

    final var beginMessage =
        "<strong>Ready to Begin</strong>,Take your first step and test your skills — we're here to help you grow!";

    if (courseLevel.isEmpty()) {
      return beginMessage;
    }
    var latestResult = courseLevel.get();
    var completedCount =
        betSectionUnitLessonsInstRepository.getStudentExamCompletedByCourse(
            authUserId,
            latestResult.getBetSectionId(),
            latestResult.getGradeSlug(),
            BetStatus.COMPLETED.ordinal());
    if (completedCount == 0) {
      return beginMessage;
    }
    var totalLessons =
        betSectionUnitLessonsInstRepository.getTotalLessonByCourseAndLevel(
            latestResult.getGradeSlug(), latestResult.getBetSectionId());

    return String.format(
        "You need %d%% More to reach %s",
        Math.round(100 - (double) completedCount / totalLessons * 100),
        getUpcomingLevel(grades, latestResult.getGradeSlug()));
  }

  private String massageLevelName(String levelSlug) {
    if (StringUtils.isEmpty(levelSlug)) {
      return levelSlug;
    }
    switch (levelSlug) {
      case "beg" -> {
        return "Beginner";
      }
      case "ele" -> {
        return "Elementary";
      }
      case "inter" -> {
        return "Intermediate";
      }
      case "upint" -> {
        return "Upper Intermediate";
      }
      case "adv" -> {
        return "Advanced";
      }
      case "pro" -> {
        return "Proficient";
      }
      default -> {
        return "Beginner";
      }
    }
  }

  private String getUpcomingLevel(List<Grade> grades, String gradeSlug) {
    var currentIndex = SKILL_ORDER.indexOf(gradeSlug);
    if (currentIndex == -1 || currentIndex == SKILL_ORDER.size() - 1) {
      return "No Upcoming Level";
    }
    var nextLevel = SKILL_ORDER.get(currentIndex + 1);
    return grades.stream()
        .map(Grade::getSlug)
        .filter(slug -> slug.equals(nextLevel))
        .findFirst()
        .map(level -> "%s Level".formatted(massageLevelName(level)))
        .orElse("No Upcoming Level");
  }

  public List<GenericMetricResponse> getContinueCoursesDashboard() {
    Map<String, Object> data = buildDashboardData();
    return List.of(GenericMetricResponse.builder().data(data).build());
  }

  public Map<String, Object> buildDashboardData() {
    Map<String, Object> data = new HashMap<>();
    List<DashBoardDto.CourseContinuationResponse> continueCourses = buildContinueCourses();
    data.put("continue_Learnings", continueCourses);
    return data;
  }

  public List<DashBoardDto.CourseContinuationResponse> buildContinueCourses() {
    var user = authService.getStudentDetails();
    var betSections = betSectionRepository.findAllByBetSectionCategoryIdInOrderBySeqNo(List.of(1L));
    var board = betSectionService.getBoardCurriculum(WEXL_INTERNAL, EBC_BOARD);
    var grades =
        board.getGrades().stream()
            .sorted(Comparator.comparing(Grade::getOrderId).reversed())
            .toList();

    return betSections.stream()
        .map(
            betSection -> {
              var betSectionInst =
                  betSectionInstRepository.findByBetSectionAndUserId(
                      betSection.getId(), user.getAuthUserId());
              var betSectionUnits = betSectionUnitRepository.findAllByBetSections(betSection);
              var betSectionCount =
                  betSectionUnitLessonsRepository.countByBetSectionUnitsIn(betSectionUnits);
              var completedLessonCount =
                  betSectionUnitLessonsInstRepository.getAttemptedLessonsByUserIdAndCourseSlug(
                      user.getAuthUserId(),
                      betSection.getWexlSubjectSlug(),
                      BetStatus.COMPLETED.ordinal());

              var unitCategory =
                  grades.stream()
                      .filter(
                          grade ->
                              betSectionService.buildStatus(
                                      grade, user.getAuthUserId(), betSection.getId())
                                  == UNLOCKED)
                      .findFirst();
              var gradeSlug = unitCategory.map(Grade::getSlug).orElse(null);
              var optionalBetSectionUnitInst =
                  betSectionUnitInstRepository.getStudentInProgressCourseLevel(
                      user.getAuthUserId(), betSection.getId(), gradeSlug);

              return DashBoardDto.CourseContinuationResponse.builder()
                  .courseTitle(betSection.getName())
                  .sectionId(betSection.getId())
                  .sectionInstId(betSectionInst.map(BetSectionInst::getId).orElse(null))
                  .sectionUnitId(
                      optionalBetSectionUnitInst
                          .map(BetSectionUnitInst::getBetSectionUnitId)
                          .orElse(null))
                  .sectionUnitInstId(
                      optionalBetSectionUnitInst.map(BetSectionUnitInst::getId).orElse(null))
                  .lessonSlug(gradeSlug)
                  .totalLessons(betSectionCount)
                  .completedLesson((long) completedLessonCount.size())
                  .build();
            })
        .toList();
  }
}
