package com.wexl.sections.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.bet.outline.dto.DocumentDto;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.sections.dto.BetSectionDto;
import com.wexl.sections.service.BetSectionService;
import com.wexl.writing.dto.WritingTaskDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class BetSectionInstController {

  private final BetSectionService betSectionService;

  @GetMapping("/bet-section-categories")
  public List<BetSectionDto.BetSectionCategoriesResponse> getBetSectionCategories() {
    return betSectionService.getBetSectionCategories();
  }

  @PostMapping("/users/{authUserId}/bet-section-insts:initialize")
  public void initializeBetSectionInsts(@PathVariable String authUserId) {
    betSectionService.initializeBetSectionInsts(authUserId);
  }

  @GetMapping("/users/{authUserId}/bet-categories/{categoryId}/bet-section-insts")
  public List<BetSectionDto.BetSectionInstResponse> getBetSectionInsts(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @PathVariable Long categoryId) {
    return betSectionService.getBetSectionInsts(authUserId, categoryId, orgSlug);
  }

  @GetMapping("/unit-categories")
  public List<BetSectionDto.UnitCategories> getUnitCategories() {
    return betSectionService.getUnitCategories();
  }

  @GetMapping(
      "/users/{authUserId}/bet-section-insts/{betSectionInstId}/unit-categories/{unitCategorySlug}/bet-section-unit-insts")
  public List<BetSectionDto.BetSectionUnitResponse> getBetSectionsUnitInsts(
      @PathVariable Long betSectionInstId,
      @PathVariable String authUserId,
      @PathVariable String unitCategorySlug) {
    return betSectionService.getBetSectionsUnitInsts(
        authUserId, betSectionInstId, unitCategorySlug);
  }

  @GetMapping(
      "/users/{authUserId}/bet-section-insts/{betSectionInstId}/bet-section-unit-insts/{betSectionUnitInstId}/bet-section-unit-lesson-insts")
  public List<BetSectionDto.BetSectionUnitLessonsResponse> getBetSectionsUnitLessons(
      @PathVariable Long betSectionInstId,
      @PathVariable Long betSectionUnitInstId,
      @PathVariable String authUserId) {
    return betSectionService.getBetSectionsUnitLessons(
        authUserId, betSectionInstId, betSectionUnitInstId);
  }

  @PostMapping(
      "/users/{authUserId}/bet-section-unit-lesson-insts/{betSectionUnitLessonInstId}/bet-exams")
  public ExamResponse startBetLesson(
      @PathVariable Long betSectionUnitLessonInstId, @PathVariable String authUserId) {
    return betSectionService.startBetLesson(betSectionUnitLessonInstId, authUserId);
  }

  @GetMapping(
      "/users/{authUserId}/bet-section-unit-lesson-insts/{betSectionUnitLessonInstId}/wiki-documents")
  public DocumentDto.WikiDocumentResponse getBetExam(
      @PathVariable Long betSectionUnitLessonInstId, @PathVariable String authUserId) {
    return betSectionService.getWikiDocument(betSectionUnitLessonInstId, authUserId);
  }

  @PostMapping(
      "/users/{authUserId}/bet-section-unit-lesson-insts/{betSectionUnitLessonInstId}/reading-material")
  public BetSectionDto.BetSectionUnitLessonReadingMaterial getLessonReadingMaterial(
      @PathVariable Long betSectionUnitLessonInstId, @PathVariable String authUserId) {
    return betSectionService.getLessonReadingMaterial(betSectionUnitLessonInstId, authUserId);
  }

  @GetMapping("/users/{authUserId}/bet-exams/{examId}/questions")
  public QuestionDto.QuestionResponse getExamQuestionResponse(
      @PathVariable String orgSlug, @PathVariable long examId) {
    return betSectionService.getExamQuestionResponse(examId);
  }

  @PostMapping("/users/{authUserId}/bet-exams/{examId}/answers")
  public QuestionDto.ValidateAnswerResponse saveExamAnswer(
      @RequestBody QuestionDto.BetAnswerValidateRequest answerRequest,
      @PathVariable String orgSlug,
      @PathVariable Long examId) {
    return betSectionService.saveExamAnswer(answerRequest, orgSlug, examId);
  }

  @GetMapping("/users/{authUserId}/bet-exams/{examId}/results")
  public QuestionDto.StudentResultsResponse getExamResult(
      @PathVariable String authUserId, @PathVariable Long examId) {
    return betSectionService.getExamResult(authUserId, examId);
  }

  @PostMapping(
      "/users/{authUserId}/bet-section-unit-lesson-insts/{betSectionUnitLessonInstId}/bet-exams/{examId}:submit")
  public BetSectionDto.SubmitResponse submitTest(
      @PathVariable Long betSectionUnitLessonInstId,
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @PathVariable Long examId) {
    return betSectionService.submitTest(betSectionUnitLessonInstId, examId, authUserId, orgSlug);
  }

  @PostMapping("/bet-exams/{examId}/speech-evaluation")
  public SpeechEvaluation.SpeechResponse evaluateSpeakingTest(
      @PathVariable String orgSlug,
      @PathVariable Long examId,
      @RequestParam("question_uuid") String questionUuid) {
    return betSectionService.evaluateSpeakingTest(examId, questionUuid);
  }

  @PostMapping("/bet-metadata-refresh")
  public void refreshMetaData(
      @RequestBody BetSectionDto.InitializeRequest initializeRequest,
      @PathVariable String orgSlug) {
    betSectionService.refreshMetaData(initializeRequest, orgSlug);
  }

  @PostMapping("bet-sections/speech-question:upload")
  public BetSectionDto.UploadFileResponse uploadSpeechRecording(
      @PathVariable String orgSlug, @RequestPart(value = "files") MultipartFile file) {
    return betSectionService.uploadSpeechRecording(file, orgSlug);
  }

  @PostMapping("/users/{authUserId}/speech-question:upload")
  public BetSectionDto.UploadFileResponse uploadSpeechRecording(
      @PathVariable String orgSlug, @PathVariable String authUserId) {
    return betSectionService.uploadSpeechRecordingByQuestion(orgSlug, authUserId);
  }

  @PostMapping("/bet-test-definitions")
  public void createTestDefinitions(
      @RequestBody BetSectionDto.TestDefinitionRequest testDefinitionRequest,
      @PathVariable String orgSlug,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    betSectionService.createTestDefinitions(testDefinitionRequest, orgSlug, bearerToken);
  }

  @PostMapping("/teachers/{teacherAuthUserId}/ebc-initialize")
  public void initializeEbc(
      @PathVariable String orgSlug, @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    betSectionService.initializeEbc(orgSlug, bearerToken);
  }

  @PostMapping("/users/{authUserId}/bet-section-units/{betSectionUnitId}/assign-units")
  public void assignUnits(@PathVariable String authUserId, @PathVariable Long betSectionUnitId) {
    betSectionService.assignUnits(authUserId, betSectionUnitId);
  }

  @PostMapping(
      "/users/{authUserId}/bet-section-units/{betSectionUnitId}/bet-section-unit-lessons/{betSectionUnitLessonId}/assign-lesson")
  public void assignLesson(
      @PathVariable String authUserId,
      @PathVariable Long betSectionUnitLessonId,
      @PathVariable Long betSectionUnitId) {
    betSectionService.assignLesson(authUserId, betSectionUnitLessonId, betSectionUnitId);
  }

  @PostMapping("/migrate-test-definitions")
  public void updateTestDefinitions() {
    betSectionService.migrateTestDefinitions();
  }

  @GetMapping("/users/{authUserId}/bet-sections/{betSectionId}/unit-categories")
  public List<BetSectionDto.UnitCategories> getUnitCategoriesByUser(
      @PathVariable String authUserId, @PathVariable Long betSectionId) {
    return betSectionService.getUnitCategoriesByUser(authUserId, betSectionId);
  }

  @PostMapping(
      "/users/{authUserId}/bet-section-units/{betSectionUnitId}/bet-section-unit-lessons/{betSectionUnitLessonId}/unlock-level")
  public BetSectionDto.AssignLevelTestResponse assignLevelTestLesson(
      @PathVariable String authUserId,
      @PathVariable Long betSectionUnitLessonId,
      @PathVariable Long betSectionUnitId) {
    return betSectionService.assignLevelTestLesson(
        authUserId, betSectionUnitLessonId, betSectionUnitId);
  }

  @GetMapping("/bet-sections/{betSectionId}/grades/{gradeSlug}/bet-section-unit-attributes")
  public BetSectionDto.UnitAttributes getBetSectionUnitAttributes(
      @PathVariable String gradeSlug, @PathVariable Long betSectionId) {
    return betSectionService.getBetSectionUnitAttributes(gradeSlug, betSectionId);
  }

  @GetMapping("/bet-sections/{betSectionId}/bet-section-units/{betSectionUnitId}")
  public BetSectionDto.BetSectionUnitResponse getBetSectionUnitById(
      @PathVariable Long betSectionId, @PathVariable Long betSectionUnitId) {
    return betSectionService.getBetSectionUnitById(betSectionId, betSectionUnitId);
  }

  @PostMapping("/bet-exams/{examId}/writing-evaluation")
  public WritingTaskDto.StudentAnalysisReportResponse writingEvaluation(
      @PathVariable Long examId, @RequestParam("question_uuid") String questionUuid) {
    return betSectionService.writingAnswerEvaluation(examId, questionUuid);
  }
}
