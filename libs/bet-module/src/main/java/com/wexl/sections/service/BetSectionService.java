package com.wexl.sections.service;

import static com.wexl.retail.commons.util.ExtensionUtil.CONTENT_TYPE_EXTENSIONS;
import static com.wexl.retail.content.model.QuestionType.SPCH;
import static com.wexl.retail.content.model.QuestionType.SUBJECTIVE;
import static com.wexl.retail.util.Constants.WEXL_INTERNAL;
import static java.time.ZoneOffset.UTC;

import com.wexl.bet.gamification.dto.EventType;
import com.wexl.bet.gamification.event.LessonCompletionPublisher;
import com.wexl.bet.outline.dto.BetOutlineWiki;
import com.wexl.bet.outline.dto.DocumentDto;
import com.wexl.bet.outline.service.BetOutlineWikiService;
import com.wexl.betcorporate.BetCorporateService;
import com.wexl.betcorporate.BetExam;
import com.wexl.reportcard.BetReportCard;
import com.wexl.reportcard.dto.BetProficiency;
import com.wexl.reportcard.dto.BetReportDto;
import com.wexl.retail.ai.AiQuestionAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.ExtensionUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.elp.service.SpeechEvaluationService;
import com.wexl.retail.model.*;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.exam.*;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.dto.PbqDto;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import com.wexl.sections.dto.BetSectionDto;
import com.wexl.sections.dto.BetUnitCategory;
import com.wexl.sections.models.*;
import com.wexl.sections.repository.*;
import com.wexl.speech.processor.speechace.AiService;
import com.wexl.writing.dto.WritingTaskDto;
import com.wexl.writing.service.WritingTaskService;
import jakarta.transaction.Transactional;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class BetSectionService {
  private final ValidationUtils validationUtils;
  private final BetSectionRepository betSectionRepository;
  private final BetSectionInstRepository betSectionInstRepository;
  private final BetSectionUnitInstRepository betSectionUnitInstRepository;
  private final BetSectionUnitLessonsInstRepository betSectionUnitLessonsInstRepository;
  private final BetSectionUnitLessonRepository betSectionUnitLessonRepository;
  private final TestDefinitionService testDefinitionService;
  private final ExamFactory examFactory;
  private final ExamTransformer examTransformer;
  private final ExamRepository examRepository;
  private final ExamService examService;
  private final ContentService contentService;
  private final ExamAnswerRepository examAnswerRepository;
  private final BetSectionUnitLessonInstAttemptsRepository
      betSectionUnitLessonInstAttemptsRepository;
  private final ScheduleTestStudentService scheduleTestStudentService;
  private final SpeechEvaluationService speechEvaluationService;
  private final BetSectionUnitRepository betSectionUnitRepository;
  private final BetSectionCategoryRepository betSectionCategoryRepository;
  private final CurriculumService curriculumService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final StorageService storageService;
  private final AuthService authService;
  private final TestQuestionRepository testQuestionRepository;
  private final BetSectionUnitAttributeRepository betSectionUnitAttributeRepository;
  private final LessonCompletionPublisher lessonCompletionPublisher;
  private final BetCorporateService betCorporateService;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final BetReportCard betReportCard;
  private final List<AiQuestionAnalysis> aiQuestionAnalysis;
  private final BetOutlineWikiService betOutlineWikiService;
  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";
  private static final String EBC_ORG = "wexl-internal";
  public static final String EBC_BOARD = "cefr";
  private final String[][] sectionNames = {
    {"eng-listen", "Listening"},
    {"eng-speak", "Speaking"},
    {"eng-read", "Reading"},
    {"eng-write", "Writing"}
  };
  private final UserRepository userRepository;
  private final AiService aiService;
  private final WritingTaskService writingTaskService;
  private static final String IMPROMPTU_SPEECH = "Impromptu Speech";

  @Value("${app.contentToken}")
  String contentBearerToken;

  public void initializeBetSectionInsts(String authUserId) {
    var user = validationUtils.isValidUser(authUserId);
    var sections = betSectionRepository.findAll();
    if (sections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.BetSections.NotFound");
    }
    sections.forEach(
        section -> {
          mapBetSectionsToUser(section, user);
          mapBetSectionUnitsToUser(section, user);
          mapBetSectionUnitsLessonsToUser(section, user);
        });
  }

  private void mapBetSectionsToUser(BetSection section, User user) {
    if (!isSectionAlreadyAssigned(user, section.getId())) {
      betSectionInstRepository.save(
          BetSectionInst.builder()
              .betSection(section.getId())
              .userId(user.getAuthUserId())
              .betStatus(BetStatus.NOT_STARTED)
              .build());
    }
  }

  private void mapBetSectionUnitsToUser(BetSection section, User user) {
    var board = getBoardCurriculum("wexl-internal", EBC_BOARD);
    var grade = board.getGrades().getFirst().getSlug();

    findFirstUnit(section, grade)
        .ifPresent(
            firstUnit -> {
              if (!isUnitAlreadyAssigned(user, firstUnit.getId())) {
                betSectionUnitInstRepository.save(
                    BetSectionUnitInst.builder()
                        .betSectionUnitId(firstUnit.getId())
                        .userId(user.getAuthUserId())
                        .betStatus(BetStatus.NOT_STARTED)
                        .build());
              }
            });
  }

  private void mapBetSectionUnitsLessonsToUser(BetSection section, User user) {
    findFirstUnit(section)
        .flatMap(this::findFirstUnitLesson)
        .ifPresent(
            firstLesson -> {
              if (!isLessonAlreadyAssigned(user, firstLesson.getId())) {
                if (firstLesson.getTestDefinitionId() == null) {
                  throw new ApiException(
                      InternalErrorCodes.INVALID_REQUEST, "error.TestDefinitionNotFound");
                }
                var testDefinition =
                    testDefinitionService.getTestDefinitionById(firstLesson.getTestDefinitionId());
                var testDefinitionSections =
                    testDefinition.getTestDefinitionSections().stream()
                        .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
                        .toList();
                var firstSection = testDefinitionSections.get(0);
                betSectionUnitLessonsInstRepository.save(
                    BetSectionUnitLessonInst.builder()
                        .betSectionUnitLessons(firstLesson.getId())
                        .testDefinitionSectionId(firstSection.getId())
                        .userId(user.getAuthUserId())
                        .betStatus(BetStatus.NOT_STARTED)
                        .seqNo(firstSection.getSequenceNumber())
                        .build());
              }
            });
  }

  private Optional<BetSection> findFirstSection(List<BetSection> sections) {
    return sections.stream().filter(section -> section.getSeqNo() == 1).findFirst();
  }

  private Optional<BetSectionUnit> findFirstUnit(BetSection section) {
    return section.getBetSectionUnits().stream().filter(unit -> unit.getSeqNo() == 1).findFirst();
  }

  private Optional<BetSectionUnit> findFirstUnit(BetSection section, String gradeSlug) {
    return section.getBetSectionUnits().stream()
        .filter(unit -> unit.getSeqNo() == 1 && unit.getGradeSlug().equals(gradeSlug))
        .findFirst();
  }

  private Optional<BetSectionUnitLesson> findFirstUnitLesson(BetSectionUnit unit) {
    return unit.getBetSectionUnitLessons().stream()
        .filter(lesson -> lesson.getSeqNo() == 1)
        .findFirst();
  }

  private boolean isSectionAlreadyAssigned(User user, Long sectionId) {
    return betSectionInstRepository.findAllByUserId(user.getAuthUserId()).stream()
        .map(BetSectionInst::getBetSection)
        .anyMatch(id -> id.equals(sectionId));
  }

  private boolean isUnitAlreadyAssigned(User user, Long unitId) {
    return betSectionUnitInstRepository.findAllByUserId(user.getAuthUserId()).stream()
        .map(BetSectionUnitInst::getBetSectionUnitId)
        .anyMatch(id -> id.equals(unitId));
  }

  private boolean isLessonAlreadyAssigned(User user, Long lessonId) {
    var authUserId = user.getAuthUserId();
    Optional<BetSectionUnitLessonInst> matchedLessonInstOpt =
        findMatchingLessonInstance(authUserId, lessonId);
    if (matchedLessonInstOpt.isEmpty()) {
      return false;
    }
    updateWithTestDefinitionSection(matchedLessonInstOpt.get());
    return true;
  }

  private Optional<BetSectionUnitLessonInst> findMatchingLessonInstance(
      String authUserId, Long lessonId) {
    return betSectionUnitLessonsInstRepository.findAllByUserId(authUserId).stream()
        .filter(inst -> Objects.equals(inst.getBetSectionUnitLessons(), lessonId))
        .findFirst();
  }

  private void updateWithTestDefinitionSection(BetSectionUnitLessonInst lessonInst) {
    var lessonData = getBetSectionUnitLessons(lessonInst.getBetSectionUnitLessons());
    if (lessonData.getTestDefinitionId() == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid lesson or test definition ID.");
    }
    List<TestDefinitionSection> sections =
        testDefinitionRepository
            .findById(lessonData.getTestDefinitionId())
            .map(TestDefinition::getTestDefinitionSections)
            .orElse(Collections.emptyList());

    if (sections.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Test definition sections are empty.");
    }

    lessonInst.setTestDefinitionSectionId(sections.get(0).getId());
    betSectionUnitLessonsInstRepository.save(lessonInst);
  }

  public List<BetSectionDto.BetSectionInstResponse> getBetSectionInsts(
      String authUserId, Long categoryId, String orgSlug) {
    validationUtils.isValidUser(authUserId);
    var sections =
        betSectionRepository.findAllByBetSectionCategoryIdInOrderBySeqNo(
            Collections.singletonList(categoryId));
    var betSectionInstList = betSectionInstRepository.findAllByUserId(authUserId);

    if (sections.isEmpty()) {
      return Collections.emptyList();
    }
    unlockModules(authUserId, orgSlug);
    return buildBetSectionsResponse(betSectionInstList, sections, authUserId);
  }

  public void unlockModules(String authUserId, String orgSlug) {
    var user = validationUtils.isValidUser(authUserId);
    if (Boolean.TRUE.equals(user.getModulesUnlocked())) {
      log.info("Modules already unlocked for user: {}", user.getAuthUserId());
      return;
    }
    var reportCardData = getStudentReportCard(user, orgSlug);
    if (reportCardData.section1Name() != null
        && reportCardData.section2Name() != null
        && reportCardData.section3Name() != null
        && reportCardData.section4Name() != null) {

      var board = getBoardCurriculum("wexl-internal", EBC_BOARD);
      var grades = board.getGrades();
      var subjects = grades.getFirst().getSubjects().stream().map(Subject::getSlug).toList();
      var betSections = betSectionRepository.findAll();

      subjects.forEach(
          subject -> {
            String level =
                getLevelName(
                    switch (subject) {
                      case "eng-listen" -> reportCardData.section1Grade();
                      case "eng-speak" -> reportCardData.section2Grade();
                      case "eng-read" -> reportCardData.section3Grade();
                      case "eng-write" -> reportCardData.section4Grade();
                      default ->
                          throw new ApiException(
                              InternalErrorCodes.INVALID_REQUEST, "Invalid subject: " + subject);
                    });

            if (level != null && !"UNKNOWN".equals(level)) {
              unlockLevels(subject, level, betSections, grades, authUserId);
            }
          });

      user.setModulesUnlocked(true);
      userRepository.save(user);
    }
  }

  private void unlockLevels(
      String subject,
      String level,
      List<BetSection> betSections,
      List<Grade> grades,
      String authUserId) {
    var listeningSection =
        betSections.stream().filter(x -> subject.equals(x.getWexlSubjectSlug())).findFirst();
    if (listeningSection.isEmpty()) return;
    var units = betSectionUnitRepository.findAllByBetSections(listeningSection.get());
    var matchedGrade = grades.stream().filter(g -> g.getName().contains(level)).findFirst();
    if (matchedGrade.isEmpty()) return;
    var gradeSeqNo = matchedGrade.get().getOrderId();

    for (int i = gradeSeqNo; i >= 1; i--) {
      final int currentSeqNo = i;
      var currentGrade = grades.stream().filter(g -> g.getOrderId() == currentSeqNo).findFirst();
      if (currentGrade.isEmpty()) continue;
      var currentUnit =
          units.stream()
              .filter(
                  u -> u.getGradeSlug().equals(currentGrade.get().getSlug()) && u.getSeqNo() == 1)
              .findFirst();

      if (currentUnit.isEmpty()) continue;
      var lesson = findFirstUnitLesson(currentUnit.get());
      lesson.ifPresent(l -> assignUnitAndLesson(currentUnit.get(), l, authUserId));
    }
  }

  public String getLevelName(String level) {
    if (level != null) {
      return switch (level) {
        case "BASIC", "BEGINNER", "ELEMENTARY" -> BetProficiency.BEGINNER.name();
        case "LOW_INTERMEDIATE", "LOW-INTERMEDIATE" -> BetProficiency.ELEMENTARY.name();
        case "INTERMEDIATE" -> BetProficiency.INTERMEDIATE.name();
        case "UPPER_INTERMEDIATE", "UPPER-INTERMEDIATE", "ADVANCED" -> "UPPER-INTERMEDIATE";
        case "PROFICIENT" -> BetProficiency.ADVANCED.name();
        case "EXPERT" -> BetProficiency.PROFICIENT.name();
        default -> "UNKNOWN";
      };
    }
    return null;
  }

  private BetReportDto.Body getStudentReportCard(User user, String orgSlug) {
    var tests = betCorporateService.getBetCorpExams(user.getAuthUserId(), orgSlug);
    var student = user.getStudentInfo();
    var completedTests =
        tests.stream()
            .filter(x -> x.status().equals("COMPLETED"))
            .sorted(Comparator.comparing(BetExam.Response::completionTime).reversed())
            .toList();
    if (!completedTests.isEmpty()) {
      var latestTest = completedTests.get(0);
      var exam = validationUtils.findByExamId(Long.valueOf(latestTest.examId()));
      var reportCardTemplate = getBetReportCardTemplateByGrade(student.getSection().getGradeSlug());

      return betReportCard.buildBody(student, exam.getScheduleTest(), reportCardTemplate.getId());
    }
    return BetReportDto.Body.builder().build();
  }

  private ReportCardTemplate getBetReportCardTemplateByGrade(String gradeSlug) {
    String templateConfig =
        Map.of("stdg", "inter-bet-report-card.xml").getOrDefault(gradeSlug, "bet-report-card.xml");

    return reportCardTemplateRepository
        .findByReportCardTemplateTypeAndConfig(ReportCardTemplateType.CUSTOM, templateConfig)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "Report card template not found"));
  }

  private List<BetSectionDto.BetSectionInstResponse> buildBetSectionsResponse(
      List<BetSectionInst> betSectionInstList, List<BetSection> sections, String authUserId) {

    return sections.stream()
        .map(
            section -> {
              var userSectionInst = findUserSectionInst(betSectionInstList, section.getId());
              var betSectionUnits = betSectionUnitRepository.findAllByBetSections(section);
              return BetSectionDto.BetSectionInstResponse.builder()
                  .seqNo(section.getSeqNo())
                  .sectionName(section.getName())
                  .sectionId(section.getId())
                  .betSectionCategoryId(section.getBetSectionCategoryId())
                  .sectionInstId(userSectionInst.map(BetSectionInst::getId).orElse(null))
                  .outCome(section.getOutcome())
                  .completedUnitsCount(buildCompletedUnits(betSectionUnits, authUserId))
                  .totalUnitsCount((long) betSectionUnits.size())
                  .pendingUnitsCount(buildPendingUnitCounts(betSectionUnits, authUserId))
                  .authUserId(userSectionInst.map(BetSectionInst::getUserId).orElse(null))
                  .betStatus(
                      userSectionInst.map(BetSectionInst::getBetStatus).orElse(BetStatus.LOCKED))
                  .title(section.getTitle())
                  .description(section.getDescription())
                  .imagePath(section.getImagePath())
                  .build();
            })
        .toList();
  }

  private Long buildPendingUnitCounts(List<BetSectionUnit> betSectionUnits, String authUserId) {
    return betSectionUnits.stream()
        .mapToLong(
            unit -> {
              var unitInst =
                  betSectionUnitInstRepository.findByUserIdAndBetSectionUnitId(
                      authUserId, unit.getId());
              return (unitInst == null
                      || unitInst.getBetStatus().equals(BetStatus.NOT_STARTED)
                      || unitInst.getBetStatus().equals(BetStatus.LOCKED)
                      || unitInst.getBetStatus().equals(BetStatus.IN_PROGRESS))
                  ? 1L
                  : 0L;
            })
        .sum();
  }

  private Long buildCompletedUnits(List<BetSectionUnit> betSectionUnits, String authUserId) {
    return betSectionUnits.stream()
        .map(
            unit ->
                betSectionUnitInstRepository.findByUserIdAndBetSectionUnitId(
                    authUserId, unit.getId()))
        .filter(Objects::nonNull)
        .filter(unitInst -> BetStatus.COMPLETED.equals(unitInst.getBetStatus()))
        .count();
  }

  private Optional<BetSectionInst> findUserSectionInst(
      List<BetSectionInst> betSectionInstList, Long sectionId) {
    return betSectionInstList.stream().filter(x -> x.getBetSection() == sectionId).findFirst();
  }

  private BetSection getBetSectionData(Long sectionId) {
    return betSectionRepository
        .findById(sectionId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Bet.SectionId.NotFound",
                    new String[] {sectionId.toString()}));
  }

  private BetSectionInst getBetSectionInstData(Long sectionInstId) {
    return betSectionInstRepository
        .findById(sectionInstId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Bet.SectionInstId.NotFound",
                    new String[] {sectionInstId.toString()}));
  }

  private BetSectionUnitInst getBetSectionUnitInstData(Long sectionUnitInstId) {
    return betSectionUnitInstRepository
        .findById(sectionUnitInstId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Bet.SectionUnitInstId.NotFound",
                    new String[] {sectionUnitInstId.toString()}));
  }

  public List<BetSectionDto.BetSectionUnitResponse> getBetSectionsUnitInsts(
      String authUserId, Long betSectionInstId, String unitCategorySlug) {
    validationUtils.isValidUser(authUserId);
    var betSectionUnitInsts = betSectionUnitInstRepository.findAllByUserId(authUserId);
    var betSectionInst = getBetSectionInstData(betSectionInstId);
    var betSection = getBetSectionData(betSectionInst.getBetSection());
    var latestUpdatedAt = getLatestUpdatedAt(betSection, unitCategorySlug, betSectionUnitInsts);
    return betSection.getBetSectionUnits().stream()
        .filter(x -> x.getGradeSlug().equals(unitCategorySlug))
        .map(
            betSectionUnit -> {
              var userSectionUnitInst =
                  findUserSectionUnitInst(betSectionUnitInsts, betSectionUnit.getId());
              boolean isLatest =
                  userSectionUnitInst
                      .map(BetSectionUnitInst::getUpdatedAt)
                      .map(updatedAt -> updatedAt.equals(latestUpdatedAt))
                      .orElse(false);
              return BetSectionDto.BetSectionUnitResponse.builder()
                  .sectionUnitInstId(
                      userSectionUnitInst.map(BetSectionUnitInst::getId).orElse(null))
                  .sectionUnitId(betSectionUnit.getId())
                  .sectionId(betSectionUnit.getBetSections().getId())
                  .authUserId(authUserId)
                  .unitName(betSectionUnit.getName())
                  .betStatus(
                      userSectionUnitInst
                          .map(BetSectionUnitInst::getBetStatus)
                          .orElse(BetStatus.LOCKED))
                  .seqNo(betSectionUnit.getSeqNo())
                  .unitCategorySlug(betSectionUnit.getGradeSlug())
                  .unitCategoryName(betSectionUnit.getGradeName())
                  .testDefinitionId(betSectionUnit.getTestDefinitionId())
                  .betUnitCategory(betSectionUnit.getBetUnitCategory())
                  .description(betSectionUnit.getDescription())
                  .isLatest(isLatest)
                  .build();
            })
        .sorted(Comparator.comparing(BetSectionDto.BetSectionUnitResponse::seqNo))
        .toList();
  }

  private Timestamp getLatestUpdatedAt(
      BetSection betSection,
      String unitCategorySlug,
      List<BetSectionUnitInst> betSectionUnitInsts) {
    return betSection.getBetSectionUnits().stream()
        .filter(x -> x.getGradeSlug().equals(unitCategorySlug))
        .map(
            betSectionUnit ->
                findUserSectionUnitInst(betSectionUnitInsts, betSectionUnit.getId())
                    .map(BetSectionUnitInst::getUpdatedAt)
                    .orElse(null))
        .filter(Objects::nonNull)
        .max(Comparator.naturalOrder())
        .orElse(null);
  }

  private Optional<BetSectionUnitInst> findUserSectionUnitInst(
      List<BetSectionUnitInst> betSectionUnitInsts, Long unitId) {
    return betSectionUnitInsts.stream().filter(x -> x.getBetSectionUnitId() == unitId).findFirst();
  }

  public List<BetSectionDto.BetSectionUnitLessonsResponse> getBetSectionsUnitLessons(
      String authUserId, Long betSectionInstId, Long betSectionUnitInstId) {

    validationUtils.isValidUser(authUserId);
    var betSectionUnitLessonsInsts =
        betSectionUnitLessonsInstRepository.findAllByUserId(authUserId);
    var betSectionInst = getBetSectionInstData(betSectionInstId);
    var betSection = getBetSectionData(betSectionInst.getBetSection());
    var betSectionUnitInst = getBetSectionUnitInstData(betSectionUnitInstId);

    var betSectionUnit =
        betSection.getBetSectionUnits().stream()
            .filter(x -> x.getId() == betSectionUnitInst.getBetSectionUnitId())
            .findFirst()
            .orElse(null);

    if (betSectionUnit == null) {
      return Collections.emptyList();
    }
    var latestTimestamp =
        betSectionUnitLessonsInsts.stream()
            .filter(x -> x.getBetStatus() != BetStatus.LOCKED)
            .map(BetSectionUnitLessonInst::getUpdatedAt)
            .filter(Objects::nonNull)
            .max(Comparator.naturalOrder())
            .orElse(null);
    return betSectionUnit.getBetSectionUnitLessons().stream()
        .map(
            lesson ->
                buildLessonResponse(
                    authUserId, lesson, betSectionUnitLessonsInsts, latestTimestamp))
        .sorted(Comparator.comparing(BetSectionDto.BetSectionUnitLessonsResponse::seqNo))
        .toList();
  }

  private BetSectionDto.BetSectionUnitLessonsResponse buildLessonResponse(
      String authUserId,
      BetSectionUnitLesson lesson,
      List<BetSectionUnitLessonInst> userLessonInsts,
      Timestamp latestTimestamp) {

    var userLessonInst = findUserLessonInst(userLessonInsts, lesson.getId());

    var lessonInsts =
        buildLessonInsts(userLessonInst, lesson.getTestDefinitionId(), latestTimestamp);

    return BetSectionDto.BetSectionUnitLessonsResponse.builder()
        .sectionUnitId(lesson.getBetSectionUnits().getId())
        .sectionId(lesson.getBetSectionUnits().getBetSections().getId())
        .authUserId(authUserId)
        .lessonName(lesson.getName())
        .seqNo(lesson.getSeqNo())
        .sectionUnitLessonId(lesson.getId())
        .testDefinitionId(lesson.getTestDefinitionId())
        .sectionUnitLessonInsts(lessonInsts)
        .isLatest(
            (lessonInsts != null && !lessonInsts.isEmpty())
                ? lessonInsts.get(0).isLatest()
                : Boolean.FALSE)
        .totalLessonsCount((lessonInsts != null) ? (long) lessonInsts.size() : 0L)
        .completedLessonsCount(
            lessonInsts != null ? buildCompletedLessonsCounts(lessonInsts) : null)
        .pendingLessonsCount(lessonInsts != null ? buildPendingLessonsCount(lessonInsts) : null)
        .lessonStatus(lessonInsts != null ? buildLessonStatus(lessonInsts) : null)
        .betUnitCategory(lesson.getBetSectionUnits().getBetUnitCategory())
        .build();
  }

  private BetStatus buildLessonStatus(
      List<BetSectionDto.BetSectionUnitLessonInstResponse> lessonInsts) {

    boolean hasCompleted = true;
    boolean hasLocked = true;
    boolean hasNotStarted = false;

    for (BetSectionDto.BetSectionUnitLessonInstResponse lesson : lessonInsts) {
      BetStatus status = lesson.betStatus();
      if (status == BetStatus.IN_PROGRESS) {
        return BetStatus.IN_PROGRESS;
      }

      if (status != BetStatus.COMPLETED) {
        hasCompleted = false;
      }

      if (status != BetStatus.LOCKED) {
        hasLocked = false;
      }

      if (status == BetStatus.NOT_STARTED) {
        hasNotStarted = true;
      }
    }

    if (hasCompleted) {
      return BetStatus.COMPLETED;
    }
    if (hasNotStarted) {
      return BetStatus.NOT_STARTED;
    }
    if (hasLocked && hasNotStarted) {
      return BetStatus.NOT_STARTED;
    }

    if (hasLocked) {
      return BetStatus.LOCKED;
    }
    return BetStatus.LOCKED;
  }

  private Long buildCompletedLessonsCounts(
      List<BetSectionDto.BetSectionUnitLessonInstResponse> lessonInsts) {
    return lessonInsts.stream().filter(x -> x.betStatus().equals(BetStatus.COMPLETED)).count();
  }

  private Long buildPendingLessonsCount(
      List<BetSectionDto.BetSectionUnitLessonInstResponse> lessonInsts) {
    return lessonInsts.stream()
        .filter(
            x ->
                x.betStatus().equals(BetStatus.LOCKED)
                    || x.betStatus().equals(BetStatus.NOT_STARTED)
                    || x.betStatus().equals(BetStatus.IN_PROGRESS))
        .count();
  }

  private List<BetSectionDto.BetSectionUnitLessonInstResponse> buildLessonInsts(
      List<BetSectionUnitLessonInst> userLessonInst,
      Long testDefinitionId,
      Timestamp latestTimestamp) {

    var testDefinition = testDefinitionService.getTestDefinitionById(testDefinitionId);
    if (testDefinition == null || testDefinition.getTestDefinitionSections() == null) {
      return Collections.emptyList();
    }

    var testDefinitionSections =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
            .toList();
    return testDefinitionSections.stream()
        .map(section -> buildLessonInstResponse(section, userLessonInst, latestTimestamp))
        .toList();
  }

  private BetSectionDto.BetSectionUnitLessonInstResponse buildLessonInstResponse(
      TestDefinitionSection section,
      List<BetSectionUnitLessonInst> userLessonInst,
      Timestamp latestTimestamp) {

    var userData =
        userLessonInst.stream()
            .filter(x -> x.getTestDefinitionSectionId().equals(section.getId()))
            .findFirst()
            .orElse(null);

    return BetSectionDto.BetSectionUnitLessonInstResponse.builder()
        .seqNo(section.getSequenceNumber())
        .sectionName(section.getName())
        .testDefinitionSectionId(section.getId())
        .lessonInstId(userData != null ? userData.getId() : null)
        .examId(userData != null ? userData.getExamId() : null)
        .betStatus(userData != null ? userData.getBetStatus() : BetStatus.LOCKED)
        .isLatest(userData != null && Objects.equals(userData.getUpdatedAt(), latestTimestamp))
        .lessonAttemptCount(
            userData != null && userData.getLessonAttemptCount() != null
                ? userData.getLessonAttemptCount()
                : 0L)
        .build();
  }

  private List<BetSectionUnitLessonInst> findUserLessonInst(
      List<BetSectionUnitLessonInst> lessonInsts, Long lessonId) {
    return lessonInsts.stream().filter(x -> x.getBetSectionUnitLessons().equals(lessonId)).toList();
  }

  public ExamResponse startBetLesson(Long betSectionUnitLessonInstId, String authUserId) {
    validationUtils.isValidUser(authUserId);

    var lessonInst = getBetSectionUnitLessonInstIdByUserId(betSectionUnitLessonInstId, authUserId);
    var lesson = getBetSectionUnitLessons(lessonInst.getBetSectionUnitLessons());
    var testDefinition = testDefinitionService.getTestDefinitionById(lesson.getTestDefinitionId());
    if (testDefinition.getTestDefinitionSections().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Config");
    }

    Exam exam = examFactory.createEbcExam(lessonInst.getId());
    exam.setAllowedDuration(Constants.ALLLOWED_DURATION);
    exam.setBetSectionUnitLessonInstId(lessonInst.getId());
    exam.setSubjectSlug(testDefinition.getSubjectSlug());
    exam.setTestDefinition(testDefinition);
    exam.setChapterName(lesson.getName());
    var examAnswers = buildExamAnswersEntity(testDefinition.getTestDefinitionSections(), exam);
    exam.setExamAnswers(examAnswers);
    examRepository.save(exam);
    updateLessonInsts(lessonInst, exam);
    saveLessonAttempts(lessonInst, exam);
    return examTransformer.mapExamToExamResponse(exam);
  }

  private void updateLessonInsts(BetSectionUnitLessonInst lessonInst, Exam exam) {
    lessonInst.setExamId(exam.getId());
    lessonInst.setStartDate(LocalDateTime.now());
    lessonInst.setEndTime(null);
    lessonInst.setScore(null);
    lessonInst.setBetStatus(
        lessonInst.getBetStatus() == BetStatus.COMPLETED
            ? BetStatus.COMPLETED
            : BetStatus.IN_PROGRESS);
    betSectionUnitLessonsInstRepository.save(lessonInst);
  }

  private void saveLessonAttempts(BetSectionUnitLessonInst lessonInst, Exam exam) {
    betSectionUnitLessonInstAttemptsRepository.save(
        BetSectionUnitLessonAttempt.builder()
            .betSectionUnitLessons(lessonInst.getBetSectionUnitLessons())
            .betSectionUnitLessonsInst(lessonInst.getId())
            .examId(exam.getId())
            .build());
  }

  public List<ExamAnswer> buildExamAnswersEntity(
      List<TestDefinitionSection> testDefinitionSections, Exam exam) {
    Set<String> questionUuids = new HashSet<>();
    Map<String, List<ExamAnswer>> examAnswerMap = buildExamAnswerMap(exam);
    return testDefinitionSections.stream()
        .flatMap(section -> section.getTestQuestions().stream())
        .filter(question -> questionUuids.add(question.getQuestionUuid()))
        .map(
            question -> {
              var possibleExamAnswers = examAnswerMap.get(question.getQuestionUuid());
              var examAnswer =
                  CollectionUtils.isEmpty(possibleExamAnswers)
                      ? new ExamAnswer()
                      : possibleExamAnswers.getFirst();
              examAnswer.setQuestionUuid(question.getQuestionUuid());
              examAnswer.setQuestionId(question.getId());
              examAnswer.setType(question.getType());
              examAnswer.setExam(exam);
              examAnswer.setExamReference(exam.getId());
              examAnswer.setActive(true);
              examAnswer.setMarksPerQuestion(question.getMarks());
              return examAnswer;
            })
        .toList();
  }

  private Map<String, List<ExamAnswer>> buildExamAnswerMap(Exam exam) {
    if (!CollectionUtils.isEmpty(exam.getExamAnswers())) {
      return exam.getExamAnswers().stream()
          .collect(Collectors.groupingBy(ExamAnswer::getQuestionUuid));
    }
    return new HashMap<>();
  }

  public QuestionDto.QuestionResponse getExamQuestionResponse(long examId) {
    var exam = validationUtils.findByExamId(examId);
    return testDefinitionService.getTestDefinitionQuestions(exam.getTestDefinition().getId(), 1);
  }

  private BetSectionUnitLessonInst getBetSectionUnitLessonInstId(Long lessonInstId) {

    var lessonInst = betSectionUnitLessonsInstRepository.findById(lessonInstId);
    if (lessonInst.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Bet.SectionUnitLessonInstId.NotFound",
          new String[] {lessonInstId.toString()});
    }
    return lessonInst.get();
  }

  private BetSectionUnitLesson getBetSectionUnitLessons(Long lessonInstId) {

    var lessons = betSectionUnitLessonRepository.findById(lessonInstId);
    if (lessons.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Bet.SectionUnitLessonId.NotFound",
          new String[] {lessonInstId.toString()});
    }
    return lessons.get();
  }

  private BetSectionUnitLessonInst getBetSectionUnitLessonInstIdByUserId(
      Long betSectionUnitLessonInstId, String authUserId) {

    var lessonInst =
        betSectionUnitLessonsInstRepository.findByIdAndUserId(
            betSectionUnitLessonInstId, authUserId);
    if (lessonInst == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Bet.SectionUnitLessonInstId.NotFound",
          new String[] {betSectionUnitLessonInstId.toString()});
    }
    return lessonInst;
  }

  public QuestionDto.ValidateAnswerResponse saveExamAnswer(
      QuestionDto.BetAnswerValidateRequest request, String orgSlug, Long examId) {
    Exam exam;
    exam = examService.findById(examId);
    Optional<ExamAnswer> optionalExamAnswer;
    optionalExamAnswer =
        exam.getExamAnswers().stream()
            .filter(x -> x.getQuestionUuid().equals(request.questionUuid()))
            .findAny();
    if (optionalExamAnswer.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Config");
    }
    return saveExamAnswerAndBuildResponse(request, optionalExamAnswer.get(), orgSlug, exam);
  }

  public QuestionDto.ValidateAnswerResponse saveExamAnswerAndBuildResponse(
      QuestionDto.BetAnswerValidateRequest studentAnswer,
      ExamAnswer examAnswer,
      String orgSlug,
      Exam exam) {

    QuestionDto.ValidateAnswerResponse questionResponse;
    questionResponse =
        contentService.validateAnswerResponse(
            studentAnswer.questionType().name(),
            studentAnswer.questionUuid(),
            orgSlug,
            studentAnswer);

    examAnswer.setSubtopicSlug(questionResponse.subtopicSlug());
    examAnswer.setQuestionUuid(questionResponse.uuid());
    examAnswer.setType(questionResponse.type().getType());
    examAnswer.setExamReference(exam.getId());
    examAnswer.setSelectedOption(studentAnswer.mcqSelectedAnswer());
    examAnswer.setFbqSelectedAnswer(studentAnswer.fbqSelectedAnswer());
    examAnswer.setAmcqSelectedAnswer(studentAnswer.amcqSelectedAnswer());
    examAnswer.setSpchSelectedAnswer(studentAnswer.spchSelectedAnswer());
    examAnswer.setMsqSelectedAnswer(studentAnswer.msqSelectedAnswer());
    examAnswer.setYesNoSelectedAnswer(studentAnswer.yesNoSelectedAnswer());
    examAnswer.setNatSelectedAnswer(studentAnswer.natSelectedAnswer());
    examAnswer.setPbqAnswers(studentAnswer.pbqSelectedAnswer());
    examAnswer.setSubjectiveWrittenAnswer(studentAnswer.subjectiveWrittenAnswer());
    examAnswer.setCorrect(questionResponse.isCorrect());
    examAnswer.setAttempted(Boolean.TRUE);
    examAnswer.setMarksScoredPerQuestion(questionResponse.marksScored());
    examAnswer.setMarksPerQuestion(questionResponse.marks());
    examAnswerRepository.save(examAnswer);
    if (SPCH.equals(studentAnswer.questionType())) {
      speechEvaluationService.speakingEvaluationAsync(
          speechEvaluationService.massage(questionResponse.question()),
          examAnswer.getSpchSelectedAnswer(),
          String.valueOf(examAnswer.getId()),
          IMPROMPTU_SPEECH.equalsIgnoreCase(questionResponse.category()));
    }
    return questionResponse;
  }

  public QuestionDto.StudentResultsResponse getExamResult(String authUserId, Long examId) {
    var exam = validationUtils.findByExamId(examId);
    BetSectionUnitLessonInst lessonInst =
        betSectionUnitLessonsInstRepository.findByUserIdAndExamIdAndId(
            authUserId, examId, exam.getBetSectionUnitLessonInstId());

    if (lessonInst == null) {
      var attempt =
          betSectionUnitLessonInstAttemptsRepository.findByBetSectionUnitLessonsInstAndExamId(
              exam.getBetSectionUnitLessonInstId(), examId);
      if (attempt == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Attempt.NotFound");
      }
      lessonInst =
          betSectionUnitLessonsInstRepository
              .findById(attempt.getBetSectionUnitLessonsInst())
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.Exam.LessonInst.NotFound"));
    }
    var testDefinition =
        testDefinitionService.getTestDefinitionById(exam.getTestDefinition().getId());
    return buildExamResults(exam, testDefinition, lessonInst);
  }

  public QuestionDto.StudentResultsResponse buildExamResults(
      Exam exam, TestDefinition testDefinition, BetSectionUnitLessonInst lessonInst) {
    var questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);
    var testDefinitionSection =
        testDefinition.getTestDefinitionSections().stream()
            .filter(x -> x.getId().equals(lessonInst.getTestDefinitionSectionId()))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Invalid TestDefinitionSection"));
    Float totalMarks =
        buildTotalMarks(questionResponse.testDefinitionSectionResponses(), lessonInst);
    return QuestionDto.StudentResultsResponse.builder()
        .examId(exam.getId())
        .testDefinitionId(testDefinition.getId())
        .testName(testDefinition.getTestName())
        .gradeName(testDefinition.getGradeSlug())
        .noOfQuestions(testDefinition.getNoOfQuestions().longValue())
        .testDefinitionSection(
            buildTestDefinitionSection(exam, questionResponse, testDefinitionSection, lessonInst))
        .totalMarksSecured(
            (exam.getMarksScored() == null || exam.getMarksScored() < 0)
                ? 0
                : exam.getMarksScored())
        .totalMarks(totalMarks)
        .percentageSecured(calculatePercentage(exam.getMarksScored(), totalMarks))
        .assetSlug(
            Objects.nonNull(testDefinition.getMetadata().getAssetSlug())
                ? testDefinition.getMetadata().getAssetSlug()
                : null)
        .build();
  }

  private Float buildTotalMarks(
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses,
      BetSectionUnitLessonInst lessonInst) {
    var testDefinitionSection =
        testDefinitionSectionResponses.stream()
            .filter(x -> x.id().equals(lessonInst.getTestDefinitionSectionId()))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Invalid TestDefinitionSection"));
    return (float)
        testDefinitionSection.questions().stream()
            .mapToDouble(question -> Optional.ofNullable(question.marks()).orElse(0))
            .sum();
  }

  private List<QuestionDto.TestDefinitionSection> buildTestDefinitionSection(
      Exam exam,
      QuestionDto.QuestionResponse questionResponse,
      TestDefinitionSection testDefinitionSection,
      BetSectionUnitLessonInst lessonInst) {
    List<QuestionDto.TestDefinitionSection> response = new ArrayList<>();
    var questionResponseData =
        questionResponse.testDefinitionSectionResponses().stream()
            .filter(x -> x.id().equals(lessonInst.getTestDefinitionSectionId()))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Invalid TestDefinitionSection"));
    var questionResult =
        scheduleTestStudentService.buildQuestionResult(
            questionResponseData.questions(),
            exam,
            Collections.singletonList(testDefinitionSection),
            1);
    response.add(
        QuestionDto.TestDefinitionSection.builder()
            .id(questionResponseData.id())
            .name(questionResponseData.name())
            .seqNo(questionResponseData.seqNo())
            .noOfQuestions(questionResponseData.noOfQuestions())
            .questionResults(questionResult)
            .marks(
                Double.valueOf(
                    String.format(
                        "%.1f",
                        questionResult.stream()
                            .mapToDouble(QuestionDto.QuestionResult::marksSecured)
                            .sum())))
            .build());
    return response;
  }

  private Float calculatePercentage(Float marksScored, Float totalMarks) {
    if (marksScored == null) {
      return 0F;
    }
    return (marksScored / totalMarks * 100) < 0 ? 0.0f : Math.round(marksScored / totalMarks * 100);
  }

  @Transactional
  public BetSectionDto.SubmitResponse submitTest(
      Long betSectionUnitLessonInstId, Long examId, String authUserId, String orgSlug) {
    var exam = validationUtils.findByExamId(examId);
    var lessonInst = getBetSectionUnitLessonInstId(betSectionUnitLessonInstId);
    if (exam.getEndTime() == null) {
      updateExam(exam);
      updateAiAnswer(exam, orgSlug);
      var lessons = getBetSectionUnitLessons(lessonInst.getBetSectionUnitLessons());
      var unit = lessons.getBetSectionUnits();

      boolean isLevelTest = unit.getBetUnitCategory().equals(BetUnitCategory.LEVEL_TEST);
      boolean shouldProceed = true;

      if (isLevelTest) {
        var examData = validationUtils.findByExamId(examId);
        var percentage = (examData.getMarksScored() / examData.getTotalMarks()) * 100;
        shouldProceed = percentage >= 80;
      }
      updateLessonAttempts(lessonInst, exam);

      if (shouldProceed) {
        updateLessonInstData(lessonInst);
        handlesLessonCompletionEvent(lessonInst, authUserId);
        assignNextLesson(lessonInst, authUserId);
        return BetSectionDto.SubmitResponse.builder()
            .examId(examId)
            .betStatus(BetStatus.PASSED)
            .build();
      }
      return BetSectionDto.SubmitResponse.builder()
          .examId(examId)
          .betStatus(BetStatus.FAILED)
          .build();
    }
    return BetSectionDto.SubmitResponse.builder()
        .examId(examId)
        .betStatus(BetStatus.COMPLETED)
        .build();
  }

  private void updateAiAnswer(Exam exam, String orgSlug) {
    exam.getExamAnswers()
        .forEach(
            answer -> {
              if (isSubjectiveAndHasAnswer(answer)) {
                var promptQuestionContents = createPromptQuestionContents(answer, orgSlug);
                var promptAnswerContents = createPromptAnswerContents(answer);
                var aiResponse =
                    getAiAnalysisResponse(promptQuestionContents, promptAnswerContents);

                updateExamAnswerWithAiAnalysis(answer, aiResponse);
              }
            });
  }

  private boolean isSubjectiveAndHasAnswer(ExamAnswer answer) {
    return QuestionType.SUBJECTIVE.toString().equalsIgnoreCase(answer.getType())
        && answer.getSubjectiveWrittenAnswer() != null;
  }

  private List<ExamAnalysis.PromptQuestionContent> createPromptQuestionContents(
      ExamAnswer answer, String orgSlug) {
    var questionData =
        contentService.getQuestionsByUuid(
            contentBearerToken, answer.getType().toUpperCase(), answer.getQuestionUuid(), orgSlug);

    var promptQuestion =
        ExamAnalysis.PromptQuestionContent.builder()
            .questionNumber(answer.getQuestionId())
            .text(questionData.questions().get(0).question())
            .marks(answer.getMarksPerQuestion())
            .answer(questionData.questions().get(0).explanation())
            .build();

    return List.of(promptQuestion);
  }

  private List<ExamAnalysis.PromptAnswerContent> createPromptAnswerContents(ExamAnswer answer) {
    var promptAnswer =
        ExamAnalysis.PromptAnswerContent.builder()
            .questionNumber(answer.getQuestionId())
            .answer(answer.getSubjectiveWrittenAnswer())
            .build();

    return List.of(promptAnswer);
  }

  private ExamAnalysis.AiQuestionAnalysisResponseList getAiAnalysisResponse(
      List<ExamAnalysis.PromptQuestionContent> promptQuestionContents,
      List<ExamAnalysis.PromptAnswerContent> promptAnswerContents) {
    return aiQuestionAnalysis
        .getFirst()
        .analyzeQuestions(promptQuestionContents, promptAnswerContents);
  }

  private void updateExamAnswerWithAiAnalysis(
      ExamAnswer answer, ExamAnalysis.AiQuestionAnalysisResponseList aiResponse) {
    var aiResponseData = aiResponse.response().get(0);
    answer.setAiAnalysis(aiResponseData.analysis());
    answer.setAiMarks((float) aiResponseData.marks());
    examAnswerRepository.save(answer);
  }

  private void handlesLessonCompletionEvent(
      BetSectionUnitLessonInst lessonInst, String authUserId) {
    if (lessonInst.getLessonAttemptCount() == 1) {
      lessonCompletionPublisher.publishEvent(authUserId, EventType.LESSON_COMPLETED);
    }
  }

  private void assignNextLesson(BetSectionUnitLessonInst lessonInst, String authUserId) {
    var lesson = getBetSectionUnitLessons(lessonInst.getBetSectionUnitLessons());
    if (Boolean.TRUE.equals(assignNextLessonInst(lessonInst, authUserId, lesson))) {
      var nextLessonSeqNo = lesson.getSeqNo() + 1;
      var unitLessons = lesson.getBetSectionUnits().getBetSectionUnitLessons();
      var nextLessonOpt =
          unitLessons.stream().filter(x -> x.getSeqNo() == nextLessonSeqNo).findFirst();

      if (nextLessonOpt.isPresent()) {
        var nextLesson = nextLessonOpt.get();
        var nextTestDefinition =
            testDefinitionService.getTestDefinitionById(nextLesson.getTestDefinitionId());
        var testDefinitionSection =
            nextTestDefinition.getTestDefinitionSections().stream()
                .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
                .toList();
        var firstSection = testDefinitionSection.get(0);

        saveLessonInstance(
            authUserId, nextLesson.getId(), firstSection.getId(), firstSection.getSequenceNumber());
      } else {
        completeUnitAndAssignNext(lesson, authUserId);
      }
    }
  }

  private Boolean assignNextLessonInst(
      BetSectionUnitLessonInst lessonInst, String authUserId, BetSectionUnitLesson lesson) {
    var testDefinition = testDefinitionService.getTestDefinitionById(lesson.getTestDefinitionId());
    var testDefinitionSection =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
            .toList();
    long nextLessonInstSeqNo = lessonInst.getSeqNo() + 1;
    var allUserLessonInsts =
        betSectionUnitLessonsInstRepository.findAllByBetSectionUnitLessonsAndUserId(
            lessonInst.getBetSectionUnitLessons(), authUserId);

    var nextTestDefinitionSection =
        testDefinitionSection.stream()
            .filter(x -> x.getSequenceNumber().equals(nextLessonInstSeqNo))
            .findFirst();
    if (nextTestDefinitionSection.isPresent()) {

      var isLessonInstAlreadyAssigned =
          allUserLessonInsts.stream()
              .filter(x -> x.getSeqNo().equals(nextLessonInstSeqNo))
              .findFirst();
      if (isLessonInstAlreadyAssigned.isEmpty()) {
        saveLessonInstance(
            authUserId,
            lessonInst.getBetSectionUnitLessons(),
            nextTestDefinitionSection.get().getId(),
            nextTestDefinitionSection.get().getSequenceNumber());
      }
    }
    return testDefinitionSection.stream()
        .allMatch(
            section ->
                allUserLessonInsts.stream()
                    .anyMatch(
                        x ->
                            x.getTestDefinitionSectionId().equals(section.getId())
                                && BetStatus.COMPLETED.equals(x.getBetStatus())));
  }

  private void saveLessonInstance(String userId, Long lessonId, Long sectionId, Long sequenceNo) {
    betSectionUnitLessonsInstRepository.save(
        BetSectionUnitLessonInst.builder()
            .userId(userId)
            .betSectionUnitLessons(lessonId)
            .testDefinitionSectionId(sectionId)
            .seqNo(sequenceNo)
            .betStatus(BetStatus.NOT_STARTED)
            .build());
  }

  private void completeUnitAndAssignNext(BetSectionUnitLesson lesson, String authUserId) {
    var unitInst =
        betSectionUnitInstRepository.findByUserIdAndBetSectionUnitId(
            authUserId, lesson.getBetSectionUnits().getId());
    unitInst.setBetStatus(BetStatus.COMPLETED);
    betSectionUnitInstRepository.save(unitInst);
    assignNextUnit(lesson, authUserId);
  }

  private void assignNextUnit(BetSectionUnitLesson lesson, String authUserId) {
    Long nextUnitSeqNo = lesson.getBetSectionUnits().getSeqNo() + 1;
    String grade = lesson.getBetSectionUnits().getGradeSlug();

    var sectionUnits = lesson.getBetSectionUnits().getBetSections().getBetSectionUnits();
    var nextUnitOpt = findNextUnit(sectionUnits, nextUnitSeqNo, grade);

    if (nextUnitOpt.isEmpty()) {
      assignNextUnitFromNextGrade(lesson, authUserId, grade, sectionUnits);
    } else {
      assignUnitAndLesson(nextUnitOpt.get(), lesson, authUserId);
    }
  }

  private Optional<BetSectionUnit> findNextUnit(
      List<BetSectionUnit> sectionUnits, Long seqNo, String grade) {
    return sectionUnits.stream()
        .filter(unit -> unit.getSeqNo().equals(seqNo) && unit.getGradeSlug().equals(grade))
        .findFirst();
  }

  private void assignNextUnitFromNextGrade(
      BetSectionUnitLesson lesson,
      String authUserId,
      String grade,
      List<BetSectionUnit> sectionUnits) {
    var board = getBoardCurriculum("wexl-internal", EBC_BOARD);
    var currentGradeOpt =
        board.getGrades().stream().filter(x -> x.getSlug().equals(grade)).findFirst();

    currentGradeOpt.ifPresentOrElse(
        currentGrade -> {
          int startOrderId = currentGrade.getOrderId() + 1;

          Map<Integer, String> orderIdToSlug =
              board.getGrades().stream()
                  .collect(Collectors.toMap(Grade::getOrderId, Grade::getSlug));

          for (int order = startOrderId; order >= 1; order--) {
            String gradeSlug = orderIdToSlug.get(order);
            if (gradeSlug != null) {
              var nextUnitOpt = findNextUnit(sectionUnits, 1L, gradeSlug);
              nextUnitOpt.ifPresent(
                  betSectionUnit -> assignUnitAndLesson(betSectionUnit, lesson, authUserId));
            }
          }
        },
        () -> assignNextBetSection(lesson, authUserId));
  }

  private void assignUnitAndLesson(
      BetSectionUnit nextUnit, BetSectionUnitLesson lesson, String authUserId) {
    var user = validationUtils.isValidUser(authUserId);

    if (!isUnitAlreadyAssigned(user, nextUnit.getId())) {
      betSectionUnitInstRepository.save(
          BetSectionUnitInst.builder()
              .betSectionUnitId(nextUnit.getId())
              .betStatus(BetStatus.NOT_STARTED)
              .userId(authUserId)
              .build());
    }

    nextUnit.getBetSectionUnitLessons().stream()
        .filter(x -> x.getSeqNo() == 1)
        .findFirst()
        .ifPresent(
            firstLesson -> {
              var testDefinition =
                  testDefinitionService.getTestDefinitionById(firstLesson.getTestDefinitionId());
              var testDefinitionSections =
                  testDefinition.getTestDefinitionSections().stream()
                      .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
                      .toList();
              var firstSection = testDefinitionSections.get(0);
              if (!isLessonAlreadyAssigned(user, lesson.getId())) {
                betSectionUnitLessonsInstRepository.save(
                    BetSectionUnitLessonInst.builder()
                        .betStatus(BetStatus.NOT_STARTED)
                        .seqNo(firstLesson.getSeqNo())
                        .betSectionUnitLessons(firstLesson.getId())
                        .testDefinitionSectionId(firstSection.getId())
                        .userId(authUserId)
                        .build());
              }
            });
  }

  private void assignNextBetSection(BetSectionUnitLesson lesson, String authUserId) {
    var currentSection = lesson.getBetSectionUnits().getBetSections();
    var nextSectionOpt =
        betSectionRepository.findAll().stream()
            .filter(x -> x.getSeqNo() == currentSection.getSeqNo() + 1)
            .findFirst();

    nextSectionOpt.ifPresent(
        nextSection -> {
          var user = validationUtils.isValidUser(authUserId);
          mapBetSectionUnitsToUser(nextSection, user);
          mapBetSectionUnitsLessonsToUser(nextSection, user);
        });
  }

  private void updateLessonAttempts(BetSectionUnitLessonInst lessonInst, Exam exam) {
    var lessonAttempt =
        betSectionUnitLessonInstAttemptsRepository
            .findByBetSectionUnitLessonsAndBetSectionUnitLessonsInstAndExamId(
                lessonInst.getBetSectionUnitLessons(), lessonInst.getId(), exam.getId());
    lessonAttempt.setScore(calculateMarkScored(exam.getExamAnswers()));
    betSectionUnitLessonInstAttemptsRepository.save(lessonAttempt);
  }

  private void updateLessonInstData(BetSectionUnitLessonInst lessonInst) {
    lessonInst.setEndTime(LocalDateTime.now());
    lessonInst.setBetStatus(BetStatus.COMPLETED);
    lessonInst.setLessonAttemptCount(
        lessonInst.getLessonAttemptCount() == null ? 1 : lessonInst.getLessonAttemptCount() + 1);
    betSectionUnitLessonsInstRepository.save(lessonInst);
  }

  private void updateExam(Exam exam) {
    exam.setCompleted(true);
    exam.setTotalMarks(
        (float) exam.getExamAnswers().stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum());
    exam.setMarksScored(calculateMarkScored(exam.getExamAnswers()));
    exam.setEndTime(Timestamp.from(Instant.now()));
    exam.setAttemptedQuestionsCount(exam.getExamAnswers().size());
    examRepository.save(exam);
  }

  private Float calculateMarkScored(List<ExamAnswer> examMarks) {
    var marks =
        (float)
            examMarks.stream()
                .filter(x -> x.isCorrect())
                .mapToDouble(ExamAnswer::getMarksScoredPerQuestion)
                .sum();
    return Float.parseFloat(Constants.DECIMAL_FORMAT.format(marks));
  }

  public SpeechEvaluation.SpeechResponse evaluateSpeakingTest(Long examId, String questionUuid) {
    return speechEvaluationService.evaluateSpeakingTest(examId, questionUuid);
  }

  public List<BetSectionDto.BetSectionCategoriesResponse> getBetSectionCategories() {
    return betSectionCategoryRepository.findAll().stream()
        .map(
            category ->
                BetSectionDto.BetSectionCategoriesResponse.builder()
                    .categoryId(category.getId())
                    .name(category.getName())
                    .build())
        .toList();
  }

  public void refreshMetaData(BetSectionDto.InitializeRequest request, String orgSlug) {
    var board = getBoardCurriculum("wexl-internal", request.boardSlug());
    var sections = saveSections(request);
    saveBetSectionUnits(sections, request, board, orgSlug);
  }

  private void saveBetSectionUnits(
      BetSection section,
      BetSectionDto.InitializeRequest request,
      EduBoard eduBoard,
      String orgSlug) {
    var existingBetSectionUnits = betSectionUnitRepository.findAllByBetSections(section);

    eduBoard
        .getGrades()
        .forEach(
            grade ->
                grade.getSubjects().stream()
                    .filter(x -> x.getSlug().equals(request.wexlSubjectSlug()))
                    .findFirst()
                    .ifPresent(
                        subject -> {
                          var chaptersList =
                              contentService.getChaptersByBoardGradeAndSubject(
                                  orgSlug, request.boardSlug(), grade.getSlug(), subject.getSlug());

                          AtomicLong seqNo = new AtomicLong(1);
                          chaptersList.forEach(
                              chapter -> {
                                BetSectionUnit betSectionUnit =
                                    existingBetSectionUnits.stream()
                                        .filter(
                                            unit ->
                                                unit.getWexlChapterSlug()
                                                    .equals(chapter.getChapterSlug()))
                                        .findFirst()
                                        .orElseGet(BetSectionUnit::new);

                                betSectionUnit.setBetSections(section);
                                betSectionUnit.setSeqNo(seqNo.getAndIncrement());
                                betSectionUnit.setName(chapter.getName());
                                betSectionUnit.setWexlChapterSlug(chapter.getChapterSlug());
                                betSectionUnit.setGradeName(grade.getName());
                                betSectionUnit.setGradeSlug(grade.getSlug());
                                betSectionUnit.setBetUnitCategory(
                                    chapter
                                            .getName()
                                            .toLowerCase()
                                            .trim()
                                            .matches(".*\\blevel test\\b.*")
                                        ? BetUnitCategory.LEVEL_TEST
                                        : BetUnitCategory.DEFAULT);
                                betSectionUnit.setBetSectionUnitLessons(
                                    buildBetSectionUnitLessons(
                                        chapter, betSectionUnit, orgSlug, eduBoard.getSlug()));

                                betSectionUnitRepository.save(betSectionUnit);
                              });
                        }));
  }

  private List<BetSectionUnitLesson> buildBetSectionUnitLessons(
      ChapterResponse chapter, BetSectionUnit betSectionUnit, String orgSlug, String boardSlug) {
    var subtopics = contentService.getSubTopicsByChapter(orgSlug, chapter.getChapterSlug());
    List<BetSectionUnitLesson> responseList = new ArrayList<>();
    var existingLessons = betSectionUnit.getBetSectionUnitLessons();

    AtomicLong seqNo = new AtomicLong(1);

    subtopics.forEach(
        subtopic -> {
          var existingLesson =
              existingLessons == null
                  ? null
                  : existingLessons.stream()
                      .filter(x -> x.getWexlSubtopicSlug().equals(subtopic.getSlug()))
                      .findFirst()
                      .orElse(null);

          if (existingLesson != null) {
            existingLesson.setName(subtopic.getName());
            existingLesson.setTestDefinitionId(getTestDefinitionId(subtopic.getSlug(), boardSlug));
            responseList.add(existingLesson);
          } else {
            BetSectionUnitLesson newLesson = new BetSectionUnitLesson();
            newLesson.setBetSectionUnits(betSectionUnit);
            newLesson.setSeqNo(seqNo.getAndIncrement());
            newLesson.setName(subtopic.getName());
            newLesson.setWexlSubtopicSlug(subtopic.getSlug());
            newLesson.setTestDefinitionId(getTestDefinitionId(subtopic.getSlug(), boardSlug));
            responseList.add(newLesson);
          }
        });

    return responseList;
  }

  private Long getTestDefinitionId(String subTopicSlug, String boardSlug) {
    var testDefinition =
        testDefinitionRepository.findByTestNameAndBoardSlugAndOrganization(
            subTopicSlug, boardSlug, EBC_ORG);
    if (testDefinition == null) {
      var td =
          testDefinitionRepository.findByTestNameAndBoardSlugAndOrganization(
              "cefr-beg-eng-listen-409819-766367", boardSlug, EBC_ORG);
      if (td == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.TestDefinition ");
      }
      return td.getId();
    }
    return testDefinition.getId();
  }

  private BetSection saveSections(BetSectionDto.InitializeRequest request) {
    var sectionCategory = getSectionCategoryById(request.sectionCategoryId());
    var betSection =
        betSectionRepository.findByWexlSubjectNameAndWexlSubjectSlugAndBetSectionCategoryIdAndName(
            request.wexlSubjectName(),
            request.wexlSubjectSlug(),
            request.sectionCategoryId(),
            request.subjectName());
    if (betSection == null) {
      return betSectionRepository.save(
          BetSection.builder()
              .wexlSubjectName(request.wexlSubjectName())
              .wexlSubjectSlug(request.wexlSubjectSlug())
              .name(request.subjectName())
              .betSectionCategoryId(sectionCategory.getId())
              .seqNo(getSeqNo())
              .build());
    }
    return betSection;
  }

  private Long getSeqNo() {
    var allBetSections = betSectionRepository.findAll();
    return allBetSections.stream()
        .map(BetSection::getSeqNo)
        .max(Comparator.naturalOrder())
        .map(seqNo -> seqNo + 1L)
        .orElse(1L);
  }

  private BetSectionCategory getSectionCategoryById(Long sectionCategoryId) {
    var sectionCategory = betSectionCategoryRepository.findById(sectionCategoryId);
    if (sectionCategory.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Bet.Category.NotFound",
          new String[] {sectionCategoryId.toString()});
    }
    return sectionCategory.get();
  }

  public List<BetSectionDto.UnitCategories> getUnitCategories() {
    List<BetSectionDto.UnitCategories> responseList = new ArrayList<>();
    var board = getBoardCurriculum("wexl-internal", EBC_BOARD);
    board
        .getGrades()
        .forEach(
            grade ->
                responseList.add(
                    BetSectionDto.UnitCategories.builder()
                        .slug(grade.getSlug())
                        .name(grade.getName())
                        .orderId(grade.getOrderId())
                        .build()));
    return responseList.stream()
        .distinct()
        .sorted(Comparator.comparing(BetSectionDto.UnitCategories::orderId))
        .toList();
  }

  public BetSectionDto.UploadFileResponse uploadSpeechRecording(
      MultipartFile multipartFile, String orgSlug) {
    if (multipartFile == null || multipartFile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Uploaded file is empty or null");
    }

    String contentType = multipartFile.getContentType();
    if (contentType == null || !CONTENT_TYPE_EXTENSIONS.containsKey(contentType)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Unsupported file type");
    }

    String reference = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC));
    String fileExtension = CONTENT_TYPE_EXTENSIONS.get(contentType);
    String originalFileName = "test.%s".formatted(fileExtension);

    try {
      return generateUploadResponse(orgSlug, multipartFile, reference, originalFileName);
    } catch (IOException e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "File upload failed: " + e.getMessage(), e);
    }
  }

  public BetSectionDto.UploadFileResponse uploadSpeechRecordingByQuestion(
      String orgSlug, String authUserId) {
    try {
      String filePath =
          "%s/speech-uploads/%s.%s".formatted(orgSlug, UUID.randomUUID().toString(), "mp3");
      Map<String, String> metadata = new HashMap<>();
      metadata.put("Content-Type", "audio/mpeg");
      metadata.put("Content-Disposition", "inline");
      var urlForUpload =
          storageService.generatePreSignedUrlForUpload("wexl-strapi-images", filePath, metadata);
      return BetSectionDto.UploadFileResponse.builder()
          .path(filePath)
          .url(urlForUpload)
          .previewUrl("https://images.wexledu.com/%s".formatted(filePath))
          .build();
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "File upload failed: " + e.getMessage(), e);
    }
  }

  private BetSectionDto.UploadFileResponse generateUploadResponse(
      String orgSlug, MultipartFile multipartFile, String reference, String originalFileName)
      throws IOException {

    byte[] fileBytes = multipartFile.getBytes();
    String url = uploadFileToStorage(fileBytes, reference, originalFileName, orgSlug);

    return BetSectionDto.UploadFileResponse.builder().path("").url(url).previewUrl(url).build();
  }

  private String uploadFileToStorage(
      byte[] fileBytes, String reference, String fileName, String orgSlug) {
    try {
      String contentType = determineContentType(fileName);
      String filePath = getFilePath(orgSlug, reference, contentType);

      storageService.uploadFile("wexl-strapi-images", contentType, filePath, fileBytes, true);

      return "https://images.wexledu.com/%s".formatted(filePath);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Error uploading file to S3: " + e.getMessage(), e);
    }
  }

  private String determineContentType(String fileName) {
    try {
      String contentType = Files.probeContentType(Path.of(fileName));
      return contentType != null ? contentType : MediaType.IMAGE_PNG_VALUE;
    } catch (IOException e) {
      log.debug("Failed to determine content type, defaulting to PNG", e);
      return MediaType.IMAGE_PNG_VALUE;
    }
  }

  public String getFilePath(String orgSlug, String reference, String contentType) {
    return "bet-sections/%s/%s%s"
        .formatted(orgSlug, reference, ExtensionUtil.getExtension(contentType));
  }

  public void createTestDefinitions(
      BetSectionDto.TestDefinitionRequest request, String orgSlug, String bearerToken) {
    var board = getBoardCurriculum(orgSlug, request.boardSlug());
    var testDefinitions =
        testDefinitionRepository.findByOrganizationAndBoardSlugAndDeletedAtIsNull(
            orgSlug, request.boardSlug());

    board
        .getGrades()
        .forEach(
            grade ->
                grade
                    .getSubjects()
                    .forEach(
                        subject ->
                            processSubject(
                                orgSlug,
                                request.boardSlug(),
                                grade,
                                subject,
                                testDefinitions,
                                bearerToken)));
  }

  private void processSubject(
      String orgSlug,
      String boardSlug,
      Grade grade,
      Subject subject,
      List<TestDefinition> testDefinitions,
      String bearerToken) {
    var chaptersList =
        contentService.getChaptersByBoardGradeAndSubject(
            orgSlug, boardSlug, grade.getSlug(), subject.getSlug());

    chaptersList.forEach(
        chapter -> {
          var subtopics = contentService.getSubTopicsByChapter(orgSlug, chapter.getChapterSlug());
          subtopics.forEach(
              subtopic -> processSubtopic(orgSlug, subtopic, testDefinitions, bearerToken));
        });
  }

  private void processSubtopic(
      String orgSlug,
      SubTopicResponse subtopic,
      List<TestDefinition> testDefinitions,
      String bearerToken) {
    boolean isTestExists =
        testDefinitions.stream().anyMatch(x -> x.getTestName().equals(subtopic.getSlug()));

    var questionsResponse = getSubtopicQuestions(subtopic, orgSlug);
    if (!questionsResponse.questions().isEmpty() && !isTestExists) {
      var testDefinition =
          createAndSaveTestDefinition(subtopic, orgSlug, questionsResponse.questions());
      publishTestDefinition(testDefinition.getId(), bearerToken);
    }
  }

  private void publishTestDefinition(Long testDefinitionId, String bearerToken) {
    testDefinitionService.publishTestDefinitionById(testDefinitionId, true, bearerToken, true);
  }

  private List<TestQuestion> buildTestQuestions(
      TestDefinitionSection section, List<QuestionDto.Question> questions) {
    List<TestQuestion> testQuestions = new ArrayList<>();
    questions.forEach(question -> testQuestions.add(createTestQuestion(section, question)));

    return testQuestions;
  }

  private QuestionDto.SearchQuestionResponse getSubtopicQuestions(
      SubTopicResponse subtopic, String orgSlug) {
    return switch (subtopic.getSubjectSlug()) {
      case "eng-listen" ->
          contentService.getQuestionsBySubjectAndSubtopics(
              orgSlug, QuestionType.AMCQ, subtopic.getSubjectSlug(), List.of(subtopic.getSlug()));
      case "eng-read" ->
          contentService.getQuestionsBySubjectAndSubtopics(
              orgSlug, QuestionType.PBQ, subtopic.getSubjectSlug(), List.of(subtopic.getSlug()));
      case "eng-write" ->
          contentService.getQuestionsBySubjectAndSubtopics(
              orgSlug,
              QuestionType.SUBJECTIVE,
              subtopic.getSubjectSlug(),
              List.of(subtopic.getSlug()));
      case "eng-speak" ->
          contentService.getQuestionsBySubjectAndSubtopics(
              orgSlug, QuestionType.SPCH, subtopic.getSubjectSlug(), List.of(subtopic.getSlug()));
      default ->
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST,
              "Invalid subject slug: " + subtopic.getSubjectSlug());
    };
  }

  private List<TestDefinitionSection> buildTestDefinitionSection(
      TestDefinition testDefinition, List<QuestionDto.Question> questions) {
    List<TestDefinitionSection> sectionList = new ArrayList<>();
    TestDefinitionSection section = new TestDefinitionSection();
    section.setTestDefinition(testDefinition);
    section.setSequenceNumber(1L);
    section.setName("Practice");
    section.setTestQuestions(buildTestQuestions(section, questions));
    sectionList.add(section);
    return sectionList;
  }

  private TestDefinition createAndSaveTestDefinition(
      SubTopicResponse subtopic, String orgSlug, List<QuestionDto.Question> questions) {
    TestDefinition testDefinition = new TestDefinition();
    testDefinition.setBoardSlug(subtopic.getBoardSlug());
    testDefinition.setGradeSlug(subtopic.getGradeSlug());
    testDefinition.setSubjectSlug(subtopic.getSubjectSlug());
    testDefinition.setCategory(TestCategory.DEFAULT);
    testDefinition.setActive(Boolean.TRUE);
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setIsAutoEnabled(Boolean.TRUE);
    testDefinition.setOrganization(orgSlug);
    testDefinition.setTestName(subtopic.getSlug());
    testDefinition.setMessage("All the Best");
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    testDefinition.setTestDefinitionSections(buildTestDefinitionSection(testDefinition, questions));
    testDefinition.setTeacher(authService.getTeacherDetails());
    testDefinition.setNoOfQuestions(questions.size());
    testDefinition.setTotalMarks(questions.stream().mapToInt(QuestionDto.Question::marks).sum());
    return testDefinitionRepository.save(testDefinition);
  }

  public EduBoard getBoardCurriculum(String orgSlug, String boardSlug) {
    var curriculum = curriculumService.getBoardsHierarchy(orgSlug);
    var board = curriculum.stream().filter(x -> x.getSlug().equals(boardSlug)).findFirst();
    if (board.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.EduboardFind.Organization",
          new String[] {boardSlug});
    }
    return board.get();
  }

  private TestQuestion createTestQuestion(
      TestDefinitionSection section, QuestionDto.Question question) {
    return TestQuestion.builder()
        .testDefinitionSection(section)
        .questionUuid(question.uuid())
        .type(question.type().name())
        .chapterSlug(question.chapterSlug())
        .chapterName(question.chapterSlug())
        .subjectiveAnswer(
            question.subjective() == null ? null : question.subjective().explanation())
        .yesNo(question.yesNo() == null ? null : question.yesNo().answer())
        .spchAnswer(question.spch() == null ? null : question.spch().answerAudioPath())
        .subjectSlug(question.subjectSlug())
        .marks(question.marks())
        .mcqAnswer(question.mcq() == null ? null : question.mcq().answer())
        .msqAnswer(question.msq() == null ? null : question.msq().answers())
        .fbqAnswer(question.fbq() == null ? null : question.fbq().answer())
        .pbqAnswers(question.pbq() == null ? null : buildPbqAnswers(question.pbq()))
        .category(question.category())
        .complexity(question.complexity())
        .natAnswer(question.nat() == null ? null : question.nat().answer())
        .build();
  }

  private PbqDto.Data buildPbqAnswers(List<QuestionDto.Pbq> pbqs) {
    List<PbqDto.Answers> answers = new ArrayList<>();
    pbqs.forEach(
        pbq -> {
          if (QuestionType.MCQ.equals(pbq.type())) {
            answers.add(
                PbqDto.Answers.builder()
                    .mcq(
                        PbqDto.Mcq.builder()
                            .questionUuid(pbq.uuid())
                            .answer(Math.toIntExact(pbq.mcq().answer()))
                            .build())
                    .build());
          }
        });
    return PbqDto.Data.builder().answers(answers).build();
  }

  public void initializeEbc(String orgSlug, String bearerToken) {
    createTestDefinitions(
        BetSectionDto.TestDefinitionRequest.builder().boardSlug(EBC_BOARD).build(),
        EBC_ORG,
        bearerToken);
    refreshData(orgSlug);
  }

  private void refreshData(String orgSlug) {
    for (String[] section : sectionNames) {
      var request =
          BetSectionDto.InitializeRequest.builder()
              .sectionCategoryId(1L)
              .subjectName(section[1])
              .wexlSubjectName(section[1])
              .boardSlug(EBC_BOARD)
              .wexlSubjectSlug(section[0])
              .build();
      refreshMetaData(request, orgSlug);
    }
  }

  public void assignUnits(String authUserId, Long betSectionUnitId) {
    var user = validationUtils.isValidUser(authUserId);
    var betSectionUnit = validateBetSectionUnit(betSectionUnitId);
    if (!isUnitAlreadyAssigned(user, betSectionUnitId)) {
      betSectionUnitInstRepository.save(
          BetSectionUnitInst.builder()
              .betSectionUnitId(betSectionUnitId)
              .userId(user.getAuthUserId())
              .betStatus(BetStatus.NOT_STARTED)
              .build());
    }
    var optionalFirstLesson = findFirstUnitLesson(betSectionUnit);
    optionalFirstLesson.ifPresent(lesson -> assignLessonToUser(user, lesson));
  }

  private BetSectionUnit validateBetSectionUnit(Long betSectionUnitId) {
    var optionalBetSectionUnit = betSectionUnitRepository.findById(betSectionUnitId);
    if (optionalBetSectionUnit.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Bet.SectionUnitId.NotFound");
    }
    return optionalBetSectionUnit.get();
  }

  public void assignLesson(String authUserId, Long betSectionUnitLessonId, Long betSectionUnitId) {
    var user = validationUtils.isValidUser(authUserId);
    var unit = validateBetSectionUnit(betSectionUnitId);
    var lesson = validateBetSectionUnitLesson(betSectionUnitLessonId, unit);
    assignLessonToUser(user, lesson);
  }

  private void assignLessonToUser(User user, BetSectionUnitLesson lesson) {
    if (!isLessonAlreadyAssigned(user, lesson.getId())) {
      if (lesson.getTestDefinitionId() == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestDefinitionNotFound");
      }
      var testDefinition =
          testDefinitionService.getTestDefinitionById(lesson.getTestDefinitionId());
      var testDefinitionSections =
          testDefinition.getTestDefinitionSections().stream()
              .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
              .toList();
      betSectionUnitLessonsInstRepository.save(
          BetSectionUnitLessonInst.builder()
              .betSectionUnitLessons(lesson.getId())
              .testDefinitionSectionId(testDefinitionSections.get(0).getId())
              .userId(user.getAuthUserId())
              .betStatus(BetStatus.NOT_STARTED)
              .seqNo(testDefinitionSections.get(0).getSequenceNumber())
              .build());
    }
  }

  private BetSectionUnitLesson validateBetSectionUnitLesson(
      Long betSectionUnitLessonId, BetSectionUnit betSectionUnit) {
    var optionalBetSectionUnitLesson =
        betSectionUnitLessonRepository.findByIdAndBetSectionUnits(
            betSectionUnitLessonId, betSectionUnit);
    if (optionalBetSectionUnitLesson == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Bet.SectionUnitId.NotFound");
    }
    return optionalBetSectionUnitLesson;
  }

  public void migrateTestDefinitions() {
    List<TestDefinition> testDefinitions =
        testDefinitionRepository.findAllBySubjectSlugAndOrganizationAndBoardSlug(
            "eng-read", EBC_ORG, "cefr");

    testDefinitions.forEach(this::updateTestDefinitionQuestions);
  }

  private void updateTestDefinitionQuestions(TestDefinition testDefinition) {
    List<TestQuestion> questions =
        testDefinition.getTestDefinitionSections().get(0).getTestQuestions();

    var contentResponse =
        contentService.getQuestionsBySubjectAndSubtopics(
            "wexl-internal", QuestionType.PBQ, "eng-read", List.of(testDefinition.getTestName()));

    questions.forEach(question -> updateQuestionIfPresent(question, contentResponse));
  }

  private void updateQuestionIfPresent(
      TestQuestion question, QuestionDto.SearchQuestionResponse contentResponse) {
    contentResponse.questions().stream()
        .filter(x -> x.uuid().equals(question.getQuestionUuid()))
        .findFirst()
        .ifPresent(
            contentQuestion -> {
              question.setPbqAnswers(buildPbqAnswers(contentQuestion.pbq()));
              testQuestionRepository.save(question);
            });
  }

  public List<BetSectionDto.UnitCategories> getUnitCategoriesByUser(
      String authUserId, Long betSectionId) {
    List<BetSectionDto.UnitCategories> responseList = new ArrayList<>();
    var board = getBoardCurriculum("wexl-internal", EBC_BOARD);
    board
        .getGrades()
        .forEach(
            grade -> {
              var status = buildStatus(grade, authUserId, betSectionId);
              var betUnitAndLessonData =
                  status == BetStatus.LOCKED
                      ? getLessonId(betSectionId, board.getGrades(), grade.getOrderId())
                      : null;
              responseList.add(
                  BetSectionDto.UnitCategories.builder()
                      .slug(grade.getSlug())
                      .name(grade.getName())
                      .orderId(grade.getOrderId())
                      .sectionUnitLessonId(
                          betUnitAndLessonData == null ? null : betUnitAndLessonData.getLessonId())
                      .sectionUnitId(
                          betUnitAndLessonData == null ? null : betUnitAndLessonData.getUnitId())
                      .betStatus(status)
                      .build());
            });
    return updateResponse(responseList);
  }

  private List<BetSectionDto.UnitCategories> updateResponse(
      List<BetSectionDto.UnitCategories> responseList) {
    if (responseList.isEmpty()) {
      return responseList;
    }

    OptionalInt maxUnlockedOrderIdOpt =
        responseList.stream()
            .filter(item -> item.betStatus() != BetStatus.LOCKED)
            .mapToInt(BetSectionDto.UnitCategories::orderId)
            .max();

    int maxUnlockedOrderId = maxUnlockedOrderIdOpt.orElse(-1);

    return responseList.stream()
        .map(
            item -> {
              String title;
              if (item.betStatus() == BetStatus.LOCKED) {
                title = "Unlock";
              } else if (item.orderId() == maxUnlockedOrderId) {
                title = "Start";
              } else {
                title = "Revisit";
              }

              return BetSectionDto.UnitCategories.builder()
                  .slug(item.slug())
                  .name(item.name())
                  .orderId(item.orderId())
                  .sectionUnitLessonId(item.sectionUnitLessonId())
                  .sectionUnitId(item.sectionUnitId())
                  .betStatus(item.betStatus())
                  .title(title)
                  .build();
            })
        .sorted(Comparator.comparing(BetSectionDto.UnitCategories::orderId))
        .toList();
  }

  private BetUnitAndLessonIds getLessonId(Long betSectionId, List<Grade> grades, Integer orderId) {
    int order = Objects.requireNonNullElse(orderId, 1) == 1 ? 1 : orderId - 1;
    return grades.stream()
        .filter(x -> x.getOrderId().equals(order))
        .map(Grade::getSlug)
        .map(slug -> betSectionUnitLessonRepository.getLessonId(betSectionId, slug))
        .filter(Objects::nonNull)
        .findFirst()
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.Bet.SectionUnitLessonInst.NotAssigned"));
  }

  public BetStatus buildStatus(Grade grade, String authUserId, Long betSectionId) {
    return betSectionUnitRepository.getAssignedUnitCounts(betSectionId, authUserId, grade.getSlug())
            == 0
        ? BetStatus.LOCKED
        : BetStatus.UNLOCKED;
  }

  public BetSectionDto.AssignLevelTestResponse assignLevelTestLesson(
      String authUserId, Long betSectionUnitLessonId, Long betSectionUnitId) {
    assignUnit(authUserId, betSectionUnitId);
    assignLesson(authUserId, betSectionUnitLessonId, betSectionUnitId);

    var lessonInst =
        betSectionUnitLessonsInstRepository.findAllByBetSectionUnitLessonsAndUserId(
            betSectionUnitLessonId, authUserId);
    if (lessonInst.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.Bet.SectionUnitLessonInst.NotAssigned");
    }

    return BetSectionDto.AssignLevelTestResponse.builder()
        .sectionUnitLessonInstId(lessonInst.get(0).getId())
        .build();
  }

  private void assignUnit(String authUserId, Long betSectionUnitId) {
    var user = validationUtils.isValidUser(authUserId);
    validateBetSectionUnit(betSectionUnitId);
    if (!isUnitAlreadyAssigned(user, betSectionUnitId)) {
      betSectionUnitInstRepository.save(
          BetSectionUnitInst.builder()
              .betSectionUnitId(betSectionUnitId)
              .userId(user.getAuthUserId())
              .betStatus(BetStatus.NOT_STARTED)
              .build());
    }
  }

  public BetSectionDto.UnitAttributes getBetSectionUnitAttributes(
      String gradeSlug, Long betSectionId) {
    var betSection = getBetSectionData(betSectionId);
    var attributes =
        betSectionUnitAttributeRepository
            .findByBetSectionAndGradeSlug(betSection, gradeSlug)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.Bet.SectionUnitAttributes",
                        new String[] {betSectionId.toString(), gradeSlug}));

    var grade = getGrade(gradeSlug);
    return BetSectionDto.UnitAttributes.builder()
        .title(attributes.getTitle() + "-" + grade.getName())
        .description(attributes.getDescription())
        .courseDetails(buildCourseDetails(attributes, grade))
        .build();
  }

  private BetSectionDto.CourseDetails buildCourseDetails(
      BetSectionUnitAttributes attributes, Grade grade) {
    LocalDateTime dateTime = LocalDateTime.now().minusDays(15);

    return BetSectionDto.CourseDetails.builder()
        .gamified("Earn XP, streaks, and unlock badges")
        .accessibleOn("Web, iOS, Android")
        .lastUpdated(DateTimeUtil.convertIso8601ToEpoch(dateTime))
        .estimatedTime("5 hours")
        .practice("5 exercises in every unit")
        .audioUnits("5")
        .level(grade.getName())
        .skillFocus(attributes.getTitle())
        .build();
  }

  private Grade getGrade(String gradeSlug) {
    var board = getBoardCurriculum("wexl-internal", EBC_BOARD);
    return board.getGrades().stream()
        .filter(x -> x.getSlug().equals(gradeSlug))
        .findFirst()
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.GradeNotFound"));
  }

  public BetSectionDto.BetSectionUnitResponse getBetSectionUnitById(
      Long betSectionId, Long betSectionUnitId) {
    var betSection = getBetSectionData(betSectionId);
    var betSectionUnit =
        betSectionUnitRepository.findByIdAndBetSections(betSectionUnitId, betSection);
    if (betSectionUnit == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Bet.SectionUnitId.NotFound",
          new String[] {betSectionUnitId.toString()});
    }
    return BetSectionDto.BetSectionUnitResponse.builder()
        .unitCategoryName(betSection.getName() + " - " + betSectionUnit.getGradeName())
        .unitName(betSectionUnit.getName())
        .build();
  }

  public BetSectionDto.BetSectionUnitLessonReadingMaterial getLessonReadingMaterial(
      Long betSectionUnitLessonInstId, String authUserId) {
    var lessonInst = getBetSectionUnitLessonInstIdByUserId(betSectionUnitLessonInstId, authUserId);
    var lesson = getBetSectionUnitLessons(lessonInst.getBetSectionUnitLessons());
    if (StringUtils.isBlank(lesson.getWikiDocumentUuid())) {
      // identify the collection name for this lesson and update it.  this is for the first time
    }
    String wikiDocumentUuid = lesson.getWikiDocumentUuid();
    //    if (StringUtils.isNotBlank(wikiDocumentUuid)) {
    //      var wikiDocument = outlineWikiService.findDocumentById(wikiDocumentUuid);
    //      if (wikiDocument.isPresent()) {
    //        return BetSectionDto.BetSectionUnitLessonReadingMaterial.builder()
    //            .id(wikiDocument.get().getId())
    //            .markdown(wikiDocument.get().getMarkdown())
    //            .title(wikiDocument.get().getTitle())
    //            .build();
    //      }
    //    }

    return null;
  }

  public DocumentDto.WikiDocumentResponse getWikiDocument(
      Long betSectionUnitLessonInstId, String authUserId) {
    var lessonInst = getBetSectionUnitLessonInstIdByUserId(betSectionUnitLessonInstId, authUserId);
    var lesson = getBetSectionUnitLessons(lessonInst.getBetSectionUnitLessons());
    return betOutlineWikiService.exploreMarkDown(
        new BetOutlineWiki.GetDocumentRequest(lesson.getWikiDocumentUuid()));
  }

  public WritingTaskDto.StudentAnalysisReportResponse writingAnswerEvaluation(
      Long examId, String questionUuid) {
    Exam exam = validationUtils.findByExamId(examId);
    final ExamAnswer answer =
        exam.getExamAnswers().stream()
            .filter(examAnswer -> examAnswer.getQuestionUuid().equals(questionUuid))
            .findFirst()
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput"));
    if (answer.getSubjectiveWrittenAnswer() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.writingQuestionUnanswered");
    }
    var response = writingTaskService.getWritingTaskAnalysis(String.valueOf(answer.getId()));
    if (Objects.nonNull(response)) {
      return response;
    }
    var testQuestion =
        contentService.getQuestionsByUuid(
            contentBearerToken,
            SUBJECTIVE.getType().toUpperCase(),
            answer.getQuestionUuid(),
            WEXL_INTERNAL);
    if (testQuestion.questions().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.Invalid.Elp.Question.Org",
          new String[] {answer.getQuestionUuid()});
    }
    WritingTaskDto.QuestionAnswerRequest request =
        WritingTaskDto.QuestionAnswerRequest.builder()
            .questionNumber(1L)
            .questionText(testQuestion.questions().getFirst().question())
            .actualAnswer(testQuestion.questions().getFirst().explanation())
            .studentAnswer(answer.getSubjectiveWrittenAnswer())
            .marks(answer.getMarksPerQuestion())
            .build();
    var analysis = writingTaskService.grammarAnalysis(request);
    writingTaskService.saveWritingTaskAnalysis(String.valueOf(answer.getId()), analysis);
    return analysis;
  }

  public ExamResponse startBetExam(BetExam.BetExamRequest request) {
    var student = authService.getStudentDetails().getStudentInfo();
    var testDefinition = testDefinitionService.getTestDefinitionById(request.testDefinitionId());
    if (testDefinition.getTestDefinitionSections().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Config");
    }
    var optionalExam = examRepository.findTop1ByStudentAndTestDefinition(student, testDefinition);
    optionalExam.ifPresent(examAnswerRepository::deleteByExam);
    var exam = optionalExam.orElse(examFactory.createBetExam());
    if (exam.isCompleted()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Test %s already attempted".formatted(testDefinition.getTestName()));
    }
    exam.setSubjectSlug(testDefinition.getSubjectSlug());
    exam.setTestDefinition(testDefinition);
    var examAnswers = buildExamAnswersEntity(testDefinition.getTestDefinitionSections(), exam);
    exam.setExamAnswers(new ArrayList<>(examAnswers));
    examRepository.save(exam);
    return examTransformer.mapExamToExamResponse(exam);
  }

  @Transactional
  public BetSectionDto.SubmitResponse submitBetExam(Long examId, String orgSlug) {
    var exam = validationUtils.findByExamId(examId);
    if (exam.getEndTime() == null) {
      updateAiAnswer(exam, orgSlug);
      updateExam(exam);

      var examData = validationUtils.findByExamId(examId);
      var percentage = (examData.getMarksScored() / examData.getTotalMarks()) * 100;

      if (percentage >= 80) {
        return BetSectionDto.SubmitResponse.builder()
            .examId(examId)
            .betStatus(BetStatus.PASSED)
            .build();
      }
      return BetSectionDto.SubmitResponse.builder()
          .examId(examId)
          .betStatus(BetStatus.FAILED)
          .build();
    }
    return BetSectionDto.SubmitResponse.builder()
        .examId(examId)
        .betStatus(BetStatus.COMPLETED)
        .build();
  }
}
