package com.wexl.sections.repository;

import com.wexl.sections.models.BetSection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BetSectionRepository extends JpaRepository<BetSection, Long> {

  List<BetSection> findAllByBetSectionCategoryIdInOrderBySeqNo(List<Long> betSectionCategory);

  BetSection findByWexlSubjectNameAndWexlSubjectSlugAndBetSectionCategoryIdAndName(
      String wexlSubjectName, String wexlSubjectSlug, Long categoryId, String subjectName);
}
