package com.wexl.sections.models;

import com.wexl.retail.model.Model;
import com.wexl.sections.dto.BetUnitCategory;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "bet_section_units")
public class BetSectionUnit extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String name;

  @Column(name = "wexl_chapter_slug")
  private String wexlChapterSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "grade_name")
  private String gradeName;

  @ManyToOne
  @JoinColumn(name = "bet_section_id")
  private BetSection betSections;

  @Column(name = "seq_num")
  private Long seqNo;

  @Column(name = "test_definition_id")
  private Long testDefinitionId;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "bet_section_unit_id")
  private List<BetSectionUnitLesson> betSectionUnitLessons;

  private BetUnitCategory betUnitCategory;
  private String description;

  private String wikiDocumentUuid;

  private String wikiCollectionUuid;
}
