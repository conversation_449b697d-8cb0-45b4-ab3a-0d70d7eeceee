package com.wexl.mytest;

import com.wexl.mytest.dto.ExamActivityDto;
import com.wexl.order.dto.OrderDto;
import com.wexl.practicetest.dto.BetPracticeTestDto;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("bet-exams/orgs/{orgSlug}")
public class MyTestController {
  private final MyTestServices myTestServices;

  @IsStudent
  @GetMapping("/students/{studentAuthId}/tests")
  public BetPracticeTestDto.ExamResponse betPracticeTest(
      @PathVariable String orgSlug, @PathVariable String studentAuthId) {
    return myTestServices.getMyTest(orgSlug, studentAuthId);
  }

  @IsStudent
  @PatchMapping("/students/{studentAuthId}/order-items/{itemId}/status")
  public void updateStatus(
      @Valid @RequestBody OrderDto.UpdateStatusRequest request,
      @PathVariable("itemId") Long orderItemId) {
    myTestServices.updateBetTestStatus(request, orderItemId);
  }

  @IsTeacherOrStudent
  @GetMapping("/students/{authUserId}/exams")
  public ExamActivityDto.ActivityResponse findExamsByStudent(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @RequestParam(value = "fetchAnalysis", defaultValue = "false") boolean fetchAnalysis) {
    return myTestServices.findExamsByStudent(orgSlug, authUserId, fetchAnalysis);
  }
}
