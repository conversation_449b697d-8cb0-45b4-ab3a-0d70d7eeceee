package com.wexl.betcorporate;

import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface BetExamRepository extends ExamRepository {

  @Query(
      value =
          """
                  SELECT  e.*  FROM exams e
                        JOIN test_schedule ts ON ts.id = e.schedule_test_id
                        JOIN test_schedule_student tss ON tss.schedule_test_id = ts.id
                        join test_definitions td  on td.id = ts.test_definition_id
                        WHERE ts.org_slug in (:orgSlugs) AND e.is_completed = true
                        and td.category in(:categoryId) and td.board_slug = 'bet' and td.organization = 'wexl-internal'""",
      nativeQuery = true)
  List<Exam> getBetExamsByOrgsAndCategory(List<String> orgSlugs, Integer categoryId);

  @Query(
      value =
          """
                  SELECT s.id AS studentId, u.id AS userId,
                        u.user_name AS userName, u.email AS userEmail,
                        CONCAT(u.first_name, ' ', u.last_name) AS name,
                        sec.name AS sectionName, u.last_login AS lastLogin,
                        CASE
                            WHEN MAX(e.end_time) >= :fromDate THEN 'ACTIVE'
                            ELSE 'INACTIVE'
                        END AS status,  u.created_at  as  userCreatedAt
                        FROM users u
                        JOIN students s ON s.user_id = u.id
                        JOIN sections sec ON sec.id = s.section_id
                        LEFT JOIN exams e ON e.student_id = s.id
                        WHERE u.organization IN (:orgSlugs)
                        GROUP BY s.id, u.id, u.user_name, u.first_name, u.last_name, u.last_login, sec.name, u.created_at, u.email
                        ORDER BY name ASC
        """,
      nativeQuery = true)
  List<LeanerActivityMetric> getLearnerActivityMetrics(
      @Param("orgSlugs") List<String> orgSlugs, @Param("fromDate") Timestamp fromDate);

  @Query(
      value =
          """
                          SELECT  e.*  FROM exams e
                                JOIN test_schedule ts ON ts.id = e.schedule_test_id
                                join test_definitions td  on td.id = ts.test_definition_id
                                WHERE e.student_id = :studentId AND e.is_completed = true
                                and td.category in(:categoryId)  and td.organization = 'wexl-internal'""",
      nativeQuery = true)
  List<Exam> getLearnerExamResultByCategory(Long studentId, List<Integer> categoryId);
}
