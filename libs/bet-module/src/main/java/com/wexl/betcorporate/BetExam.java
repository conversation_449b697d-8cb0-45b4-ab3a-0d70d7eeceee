package com.wexl.betcorporate;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.school.domain.TestCategory;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder
public record BetExam() {
  @Builder
  public record Response(
      @JsonProperty("test_id") Long testId,
      String title,
      @JsonProperty("total_questions") Integer totalQuestions,
      @JsonProperty("total_marks") Integer totalMarks,
      String status,
      @JsonProperty("exam_id") Integer examId,
      @JsonProperty("completion_time") Long completionTime,
      @JsonProperty("schedule_id") Integer scheduleId,
      @JsonProperty("tss_uuid") String tssUuid,
      @JsonProperty("test_category") TestCategory testCategory,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName) {}

  public record Request(Long testDefinitionId, String authId) {}

  @Builder
  public record ScheduleResponse(
      @JsonProperty("tss_uuid") String tssUuid,
      @JsonProperty("test_schedule_id") Long testScheduleId,
      @JsonProperty("test_definition") Long testDefinitionId) {}

  public record BetExamRequest(
      @JsonProperty("test_definition_id") @NotNull Long testDefinitionId) {}
}
