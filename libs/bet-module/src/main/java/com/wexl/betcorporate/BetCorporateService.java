package com.wexl.betcorporate;

import static com.wexl.retail.test.schedule.service.ScheduleTestService.ACTIVE;

import com.wexl.bet.gamification.repository.UserGameStatisticsRepository;
import com.wexl.metricshandler.BetExamService;
import com.wexl.reportcard.BetReportCard;
import com.wexl.reportcard.dto.BetReportDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.dto.TestDetailsUser;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.sections.repository.BetSectionUnitLessonRepository;
import jakarta.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BetCorporateService {
  private final OrgSettingsService orgSettingsService;
  private final OrganizationRepository organizationRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final SectionRepository sectionRepository;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;
  private final StudentAttributeService studentAttributeService;
  private final ScheduleTestService scheduleTestService;
  private final ValidationUtils validationUtils;
  private final ScheduleTestRepository scheduleTestRepository;
  private final GuardianService guardianService;
  private final DateTimeUtil dateTimeUtil;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final BetSectionUnitLessonRepository sectionUnitLessonRepository;
  private final BetReportCard betReportCard;
  private final BetExamRepository examRepository;
  private final UserGameStatisticsRepository userGameStatisticsRepository;
  private final StrapiService strapiService;
  private final BetExamService betExamService;
  public static String WEXL_INTERNAL = "wexl-internal";
  public static List<String> CORPORATE_BOARDS = List.of("bet", "stdb");
  public static String BET_CORP = "BET-CE-%";
  public static String BRILLIANT_INSTITUTE_ORG = "bri645604";

  public static List<Long> BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS = List.of(9203430L);

  public void initializeBet(BetInitializeRequest betInitializeRequest) {
    var org = orgSettingsService.validateOrganizaiton(betInitializeRequest.getOrgSlug());
    org.setIsCorporate(
        !Constants.BHARAT_ENGLISH_TEST_ORG.equals(betInitializeRequest.getOrgSlug()));
    organizationRepository.save(org);

    var testDefinitions =
        testDefinitionRepository.getTestDefinitionsByOrgAndBoard(
            "wexl-internal", betInitializeRequest.getBoardSlug(), "BET-CE-%", null);

    var sections =
        sectionRepository.getSectionsUsingGradeSlugsAndBoardSlugs(
            Collections.singletonList("stdg"), org.getSlug(), "bet");

    var students = studentRepository.getListStudentsBySections(sections);

    if (sections.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionNotFound");
    }

    if (testDefinitions.isEmpty() || students.isEmpty()) {
      return;
    }

    for (Student student : students) {
      var user = userRepository.findById(student.getUserInfo().getId()).orElseThrow();
      Map<String, String> attributes = new HashMap<>();
      int attributeIndex = 1;

      for (int cycle = 0; cycle < 5; cycle++) {
        for (TestDefinition td : testDefinitions) {
          var scheduleRequest =
              SimpleScheduleTestRequest.builder()
                  .testDefinitionId(td.getId())
                  .allStudents(false)
                  .message("All the Best")
                  .startDate(Instant.now().toEpochMilli())
                  .endDate(
                      LocalDateTime.now()
                          .plusMonths(6)
                          .atZone(ZoneId.systemDefault())
                          .toInstant()
                          .toEpochMilli())
                  .duration(120)
                  .metadata(
                      buildScheduleTestMetaData(sections.stream().map(Section::getName).toList()))
                  .studentIds(Collections.singleton(student.getId()))
                  .orgSlug(user.getOrganization())
                  .build();

          var response = scheduleTestService.scheduleBetTest(td, scheduleRequest, user);
          attributes.put("bet_corp_schedule_" + attributeIndex++, String.valueOf(response.getId()));
        }
      }
      var buildAttributes = StudentAttributeDto.Request.builder().attributes(attributes).build();
      studentAttributeService.saveStudentDefinitionAttributes(
          user.getAuthUserId(), user.getOrganization(), buildAttributes);
    }
  }

  public ScheduleTestMetadata buildScheduleTestMetaData(List<String> sections) {
    return ScheduleTestMetadata.builder().board("bet").grade("stdg").sections(sections).build();
  }

  public List<BetExam.Response> getBetCorpExams(String authId, String orgSlug) {
    return getBetExamsByCategory(
        authId,
        orgSlug,
        List.of(
            TestCategory.BET_CE.ordinal(),
            TestCategory.BET_IN_CE.ordinal(),
            TestCategory.BET_FI_CE.ordinal()));
  }

  public List<BetExam.Response> getBetExamsByCategory(
      String authId, String orgSlug, List<Integer> testCategories) {
    var user = userRepository.findByAuthUserId(authId).orElseThrow();
    var org = orgSettingsService.validateOrganizaiton(orgSlug);
    if (Boolean.FALSE.equals(org.getIsCorporate())) {
      return Collections.emptyList();
    }
    return getUserBetCorpTests(user, orgSlug, testCategories).stream()
        .collect(
            Collectors.toMap(
                TestDetailsUser::getTestName,
                testDefinition -> testDefinition,
                (existing, replacement) ->
                    "COMPLETED".equalsIgnoreCase(replacement.getStatus()) ? replacement : existing))
        .values()
        .stream()
        .map(testDefinition -> buildBetResponse(testDefinition, new HashMap<>()))
        .toList();
  }

  private BetExam.Response buildBetResponse(
      final @NotNull TestDetailsUser testDefinition, final @NotNull Map<String, Grade> gradeMap) {
    if (testDefinition == null) {
      return null;
    }
    final var grade = gradeMap.get(testDefinition.getGradeSlug());
    return BetExam.Response.builder()
        .title(testDefinition.getTestName())
        .totalMarks(testDefinition.getTotalMarks())
        .totalQuestions(testDefinition.getTotalQuestions())
        .testId(testDefinition.getTestDefinitionId())
        .status(testDefinition.getStatus())
        .examId(testDefinition.getExamId())
        .completionTime(
            testDefinition.getCompletedDate() != null
                ? dateTimeUtil.convertTimeStampToLong(testDefinition.getCompletedDate())
                : null)
        .scheduleId(testDefinition.getScheduleId() != null ? testDefinition.getScheduleId() : null)
        .tssUuid(testDefinition.getTssUuid())
        .testCategory(TestCategory.values()[testDefinition.getTestCategory()])
        .gradeSlug(testDefinition.getGradeSlug())
        .gradeName(Objects.nonNull(grade) ? grade.getName() : null)
        .build();
  }

  private List<TestDetailsUser> getUserBetCorpTests(
      final User user, final String orgSlug, List<Integer> testCategories) {
    if (BRILLIANT_INSTITUTE_ORG.equals(orgSlug)) {
      return testDefinitionRepository.getTestDefinitionsByOrgAndBoardAndUser(
          WEXL_INTERNAL,
          CORPORATE_BOARDS,
          testCategories,
          null,
          user.getId(),
          !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.isEmpty()
              ? BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS
              : null);
    }
    var testDetailsUsers =
        testDefinitionRepository.getTestDefinitionsByOrgAndBoardAndUser(
            WEXL_INTERNAL, CORPORATE_BOARDS, testCategories, null, user.getId(), null);

    var attemptedTests =
        scheduleTestStudentRepository.getStudentAttemptedTestsByCategory(
            WEXL_INTERNAL, CORPORATE_BOARDS, List.of(TestCategory.BET_LEGACY), user.getId());
    testDetailsUsers.addAll(attemptedTests);

    return testDetailsUsers.stream()
        .filter(
            testDetailsUser ->
                !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.contains(
                    testDetailsUser.getTestDefinitionId()))
        .toList();
  }

  private List<TestDetailsUser> getBetCorpTests(final String orgSlug) {
    if (BRILLIANT_INSTITUTE_ORG.equals(orgSlug)) {
      return testDefinitionRepository.getCorporateTestsByOrganizationAndBoardSlug(
          WEXL_INTERNAL,
          CORPORATE_BOARDS,
          null,
          List.of(
              TestCategory.BET_CE.ordinal(),
              TestCategory.BET_IN_CE.ordinal(),
              TestCategory.BET_FI_CE.ordinal()),
          !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.isEmpty()
              ? BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS
              : null);
    }
    var testDetailsUsers =
        testDefinitionRepository.getCorporateTestsByOrganizationAndBoardSlug(
            WEXL_INTERNAL,
            CORPORATE_BOARDS,
            null,
            List.of(
                TestCategory.BET_CE.ordinal(),
                TestCategory.BET_IN_CE.ordinal(),
                TestCategory.BET_FI_CE.ordinal()),
            null);

    return testDetailsUsers.stream()
        .filter(
            testDetailsUser ->
                !BRILLIANT_INSTITUTE_CUSTOM_TEST_IDS.contains(
                    testDetailsUser.getTestDefinitionId()))
        .toList();
  }

  public BetExam.ScheduleResponse scheduleBetExam(BetExam.Request request) {
    var user = guardianService.validateUser(request.authId());
    var testDefinition = validationUtils.validateTestDefinition(request.testDefinitionId());
    var scheduleTestStudent =
        scheduleTestStudentRepository.findByTestDefinitionAndStudentId(
            user.getId(), testDefinition.getId());
    if (scheduleTestStudent.isPresent()) {
      return validateBetCorpExam(scheduleTestStudent.get());
    }
    var scheduleTest = buildScheduleForStudent(user, testDefinition, new ScheduleTest());
    scheduleTest.setScheduleTestStudent(
        scheduleTestService.getScheduleTestStudents(
            Collections.singleton(user.getId()), scheduleTest));
    var scheduleTestById = scheduleTestRepository.save(scheduleTest);
    scheduleTestService.saveStudentScheduleTestAnswers(scheduleTestById);
    return BetExam.ScheduleResponse.builder()
        .testScheduleId(scheduleTest.getId())
        .tssUuid(scheduleTest.getScheduleTestStudent().getFirst().getUuid())
        .testDefinitionId(request.testDefinitionId())
        .build();
  }

  private BetExam.ScheduleResponse validateBetCorpExam(
      @NotNull ScheduleTestStudent scheduleTestStudent) {
    var scheduleTest = scheduleTestStudent.getScheduleTest();
    if (Constants.STARTED.equals(scheduleTestStudent.getStatus())
        || Constants.PENDING.equals(scheduleTestStudent.getStatus())) {
      return BetExam.ScheduleResponse.builder()
          .testScheduleId(scheduleTest.getId())
          .tssUuid(scheduleTestStudent.getUuid())
          .testDefinitionId(scheduleTest.getTestDefinition().getId())
          .build();
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Test already attempted");
  }

  private ScheduleTest buildScheduleForStudent(
      User user, TestDefinition testDef, ScheduleTest scheduleTest) {

    scheduleTest.setStartDate(LocalDateTime.now());
    scheduleTest.setEndDate(LocalDateTime.now().plusMonths(6));
    scheduleTest.setStatus(ACTIVE);
    scheduleTest.setMessage("All The Best!");
    scheduleTest.setDuration(getDurationByOrgAndTest(user.getOrganization(), testDef.getId()));
    scheduleTest.setAllStudents(false);
    scheduleTest.setTestDefinition(testDef);
    scheduleTest.setMetadata(
        buildScheduleTestMetaData(
            Collections.singletonList(user.getStudentInfo().getSection().getName())));
    scheduleTest.setTeacher(scheduleTestService.getAdminTeacher());
    scheduleTest.setPublished("false");
    scheduleTest.setType(Constants.DEFAULT_EXAM_SCHEDULETYPE);
    scheduleTest.setOrgSlug(user.getStudentInfo().getUserInfo().getOrganization());
    return scheduleTest;
  }

  private int getDurationByOrgAndTest(String organization, Long testDefinitionId) {
    if (BRILLIANT_INSTITUTE_ORG.contains(organization) && testDefinitionId.equals(9203430L)) {
      return 15;
    }
    return 60;
  }

  public List<GenericMetricResponse> getCorporateMetrics(String orgSlug) {
    var organization = validationUtils.isOrgValid(orgSlug);
    var orgSlugs = getChildOrgs(organization);
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    var localDateTime = LocalDateTime.now();
    var previousWeek = localDateTime.minusWeeks(1);

    // summary
    final var leanerActivityMetrics =
        examRepository.getLearnerActivityMetrics(
            orgSlugs, Timestamp.valueOf(LocalDateTime.now().minusMonths(1)));

    final var previousWeekTotalLearners =
        examRepository.getLearnerActivityMetrics(
            orgSlugs, Timestamp.valueOf(LocalDateTime.now().minusWeeks(1)));

    var activeLearners =
        leanerActivityMetrics.stream()
            .filter(learner -> "ACTIVE".equals(learner.getStatus()))
            .toList();

    var previousWeekActiveLearners =
        previousWeekTotalLearners.stream()
            .filter(learner -> "ACTIVE".equals(learner.getStatus()))
            .toList();

    var previousWeekTotalUserCount =
        leanerActivityMetrics.stream()
            .filter(learner -> previousWeek.isBefore(learner.getUserCreatedAt().toLocalDateTime()))
            .count();

    var totalLearnersCount = leanerActivityMetrics.size();
    var activeLearnersCount = activeLearners.size();

    Map<String, Object> summary = new HashMap<>();
    summary.put("total_learners", totalLearnersCount);
    summary.put("total_learners_progression_count", previousWeekTotalUserCount);
    summary.put("active_learners", activeLearnersCount);
    summary.put("inactive_learners", (totalLearnersCount - activeLearnersCount));
    summary.put(
        "attempted_percentage",
        totalLearnersCount > 0
            ? Math.round((activeLearnersCount / (double) totalLearnersCount) * 100.0)
            : 0.0);
    summary.put(
        "active_learners_progression_count",
        activeLearnersCount - previousWeekActiveLearners.size());
    summary.put("categories_count", 4);
    summary.put("cerf_levels", 6);
    summary.put("total_lessons", sectionUnitLessonRepository.count());

    // bet initial exam metrics

    Map<String, Object> data = new LinkedHashMap<>();

    var betExams =
        examRepository.getBetExamsByOrgsAndCategory(orgSlugs, TestCategory.BET_IN_CE.ordinal());

    var initialMarkScoredSum = betExams.stream().mapToDouble(Exam::getMarksScored).sum();
    var initialTotalMarkSum = betExams.stream().mapToDouble(Exam::getTotalMarks).sum();

    var previousWeekInitialExamResults =
        betExams.stream()
            .filter(
                result ->
                    localDateTime.minusWeeks(1).isBefore(result.getEndTime().toLocalDateTime()))
            .toList();

    var initialExamAttemptedCount = betExams.size();

    data.put(
        "initial_bet_average_score",
        betReportCard.getBetScore((initialMarkScoredSum / initialTotalMarkSum) * 100));
    data.put("initial_exam_attempted_count", initialExamAttemptedCount);
    data.put("initial_exam_not_attempted_count", (totalLearnersCount - initialExamAttemptedCount));
    data.put(
        "initial_exam_progression_count",
        (initialExamAttemptedCount - previousWeekInitialExamResults.size()));

    var basicLevelReachingLearnersCount = new AtomicInteger(0);
    var upperIntermediateReachingLearnersCount = new AtomicInteger(0);
    var expertLevelReachingLearnersCount = new AtomicInteger(0);

    buildBetScoreGraph(
        betExams,
        basicLevelReachingLearnersCount,
        upperIntermediateReachingLearnersCount,
        expertLevelReachingLearnersCount);

    data.put("score_0_to_3_learners_count", basicLevelReachingLearnersCount);
    data.put("score_3_to_6_learners_count", upperIntermediateReachingLearnersCount);
    data.put("score_6_to_9_learners_count", expertLevelReachingLearnersCount);

    // Practice bet exam
    var userGameStatisticsDetails =
        userGameStatisticsRepository.getUserGameStatisticsDetails(organization.getSlug());

    var practiceBetExams =
        examRepository.getBetExamsByOrgsAndCategory(orgSlugs, TestCategory.BET_CE.ordinal());

    var practiceBetScoreSum = practiceBetExams.stream().mapToDouble(Exam::getMarksScored).sum();

    var totalPracticeBetScoreSum =
        practiceBetExams.stream().mapToDouble(Exam::getMarksScored).sum();

    var previousWeekPracticeExams =
        practiceBetExams.stream()
            .filter(
                result ->
                    localDateTime.minusWeeks(1).isBefore(result.getEndTime().toLocalDateTime()))
            .toList();

    data.put(
        "practice_bet_average_score",
        betReportCard.getBetScore(practiceBetScoreSum / totalPracticeBetScoreSum * 100));

    data.put(
        "practice_exam_progression_count",
        previousWeekPracticeExams.isEmpty()
            ? 0
            : Constants.DECIMAL_FORMAT.format(
                (double) (practiceBetExams.size() - previousWeekPracticeExams.size())
                    / practiceBetExams.size()
                    * 100.0));

    data.put(
        "daily_streaks",
        Constants.DECIMAL_FORMAT.format(userGameStatisticsDetails.getTotalCurrentStreak()));
    data.put("daily_streaks_progression_percentage", Constants.DECIMAL_FORMAT.format(100));

    data.put(
        "xp_points_learners_average", Math.round(userGameStatisticsDetails.getAverageXpPoints()));

    data.put(
        "xp_points__progression_percentage",
        Constants.DECIMAL_FORMAT.format(
            ((userGameStatisticsDetails.getAverageXpPoints()
                        - userGameStatisticsDetails.getLastWeekXpPoints())
                    / userGameStatisticsDetails.getAverageXpPoints())
                * 100));

    // Final bet exam
    var finalBetExams =
        examRepository.getBetExamsByOrgsAndCategory(orgSlugs, TestCategory.BET_FI_CE.ordinal());
    var finalMarkScoredSum = finalBetExams.stream().mapToDouble(Exam::getMarksScored).sum();
    var finalTotalMarkSum = finalBetExams.stream().mapToDouble(Exam::getTotalMarks).sum();

    var previousWeekFinalExamResults =
        finalBetExams.stream()
            .filter(
                result ->
                    localDateTime.minusWeeks(1).isBefore(result.getEndTime().toLocalDateTime()))
            .toList();

    data.put(
        "final_bet_average_score",
        betReportCard.getBetScore((finalMarkScoredSum / finalTotalMarkSum) * 100));
    data.put("final_exam_attempted_count", finalBetExams.size());
    data.put("final_exam_not_attempted_count", (totalLearnersCount - finalBetExams.size()));
    data.put(
        "final_exam_progression_count",
        (finalBetExams.size() - previousWeekFinalExamResults.size()));

    var finalBasicLevelReachingLearnersCount = new AtomicInteger(0);
    var finalUpperIntermediateReachingLearnersCount = new AtomicInteger(0);
    var finalExpertLevelLeachingLearnersCount = new AtomicInteger(0);

    buildBetScoreGraph(
        finalBetExams,
        finalBasicLevelReachingLearnersCount,
        finalUpperIntermediateReachingLearnersCount,
        finalExpertLevelLeachingLearnersCount);
    data.put("final_0_to_3_learners_count", finalBasicLevelReachingLearnersCount);
    data.put("final_3_to_6_learners_count", finalUpperIntermediateReachingLearnersCount);
    data.put("final_6_to_9_learners_count", finalExpertLevelLeachingLearnersCount);

    genericMetricResponses.add(GenericMetricResponse.builder().summary(summary).data(data).build());
    return genericMetricResponses;
  }

  private void buildBetScoreGraph(
      List<Exam> betExams,
      AtomicInteger basicLevelReachingLearnersCount,
      AtomicInteger upperIntermediateReachingLearnersCount,
      AtomicInteger expertLevelLeachingLearnersCount) {
    betExams.stream()
        .filter(Exam::isCorrected)
        .forEach(
            e -> {
              double percentage = (e.getMarksScored() / e.getTotalMarks()) * 100;
              var betScore = betReportCard.getBetScore(percentage);
              if (betScore <= 3.0) {
                basicLevelReachingLearnersCount.incrementAndGet();
              } else if (betScore > 3.0 && betScore <= 6.0) {
                upperIntermediateReachingLearnersCount.incrementAndGet();
              } else if (betScore > 6.0) {
                expertLevelLeachingLearnersCount.incrementAndGet();
              }
            });
  }

  private List<String> getChildOrgs(Organization organization) {
    List<String> orgSlugs = new ArrayList<>();
    if (Boolean.TRUE.equals(organization.getIsParent())) {
      var childOrgs = organizationRepository.findByParentAndDeletedAtIsNull(organization);
      var childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();
      orgSlugs.addAll(childOrgSlugs);
    }
    orgSlugs.add(organization.getSlug());
    return orgSlugs;
  }

  public List<GenericMetricResponse> getAnalyticLearners(String orgSlug) {

    var organization = validationUtils.isOrgValid(orgSlug);
    var orgSlugs = getChildOrgs(organization);

    List<Exam> betExams =
        examRepository.getBetExamsByOrgsAndCategory(orgSlugs, TestCategory.BET_IN_CE.ordinal());

    Map<Long, Optional<Exam>> betExamMap =
        betExams.stream()
            .collect(
                Collectors.groupingBy(
                    e -> e.getStudent().getId(),
                    Collectors.maxBy(Comparator.comparing(Exam::getCreatedAt))));
    var leanerActivityMetrics =
        examRepository.getLearnerActivityMetrics(
            orgSlugs, Timestamp.valueOf(LocalDateTime.now().minusMonths(1)));

    var userGameStatistics = userGameStatisticsRepository.getUserGameStatsByOrgs(orgSlugs);
    var userGameStatisticsMap =
        userGameStatistics.stream()
            .collect(Collectors.toMap(ugs -> ugs.getUser().getId(), Function.identity()));

    return leanerActivityMetrics.stream()
        .map(
            learner -> {
              var data = new LinkedHashMap<String, Object>();
              var userId = learner.getUserId();
              data.put("student_id", learner.getStudentId());
              data.put("user_id", userId);
              data.put("user_name", learner.getUserName());
              data.put("user_email", learner.getUserEmail());
              data.put("student_name", learner.getName());
              data.put("batch_name", learner.getSectionName());
              var optionalExam = betExamMap.getOrDefault(learner.getStudentId(), Optional.empty());
              data.put(
                  "initial_bet_score",
                  optionalExam
                      .map(
                          exam ->
                              betReportCard.getBetScore(
                                  (double) (exam.getMarksScored() / exam.getTotalMarks() * 100)))
                      .orElse(null));
              var gameStatistics = userGameStatisticsMap.get(userId);

              data.put(
                  "xp_points",
                  Objects.nonNull(gameStatistics) ? gameStatistics.getTotalXp() : null);
              data.put(
                  "streaks_count",
                  Objects.nonNull(gameStatistics) ? gameStatistics.getCurrentStreak() : null);
              data.put("status", learner.getStatus());
              data.put(
                  "last_active_date",
                  Objects.nonNull(learner.getLastLogin())
                      ? dateTimeUtil.convertTimeStampToLong(
                          new Timestamp(learner.getLastLogin().getTime()))
                      : null);
              return GenericMetricResponse.builder().data(data).build();
            })
        .toList();
  }

  public List<BetExam.Response> getBetLabExamsByCategoryAndStudent(
      String authUserId, String orgSlug, TestCategory testCategory) {
    var student = studentRepository.getStudentByAuthUserIdAndOrgSlug(authUserId, orgSlug);
    var testDetailsUsers =
        testDefinitionRepository.getLabTestsByStudent(
            WEXL_INTERNAL, CORPORATE_BOARDS, testCategory.ordinal(), null, student.getId());
    var gradeMap =
        strapiService.getAllGrades().stream()
            .collect(Collectors.toMap(Grade::getSlug, Function.identity()));
    return testDetailsUsers.stream()
        .map(testDetail -> buildBetResponse(testDetail, gradeMap))
        .toList();
  }

  public List<GenericMetricResponse> getLearnerActivities(String org, String studentAuthId) {

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();

    var student = validationUtils.validateStudentByAuthId(studentAuthId, org);

    Map<String, Object> summary = new LinkedHashMap<>();
    summary.put("cerf_levels", 6);
    summary.put("total_lessons", sectionUnitLessonRepository.count());
    Map<String, Object> data = new LinkedHashMap<>();

    var betExams =
        examRepository.getLearnerExamResultByCategory(
            student.getId(),
            List.of(
                TestCategory.BET_IN_CE.ordinal(),
                TestCategory.BET_FI_CE.ordinal(),
                TestCategory.BET_CE.ordinal()));

    var initialOptionalExam =
        betExams.stream()
            .filter(exam -> TestCategory.BET_IN_CE.equals(exam.getTestDefinition().getCategory()))
            .findFirst();

    var finalBetOptionalExam =
        betExams.stream()
            .filter(exam -> TestCategory.BET_FI_CE.equals(exam.getTestDefinition().getCategory()))
            .findFirst();

    var betPracticeExams =
        betExams.stream()
            .filter(exam -> TestCategory.BET_CE.equals(exam.getTestDefinition().getCategory()))
            .count();

    if (initialOptionalExam.isPresent()) {
      var exam = initialOptionalExam.get();
      data.put(
          "initial_bet_score",
          betReportCard.getBetScore((double) (exam.getMarksScored() / exam.getTotalMarks() * 100)));
      data.put("initial_exam_id", exam.getId());
      data.put("initial_total_marks", exam.getTotalMarks());
      data.put("initial_marks_scored", exam.getMarksScored());
      data.put("initial_bet_status", exam.isCompleted() ? "COMPLETED" : "IN_PROGRESS");
      data.put("initial__completion_time", dateTimeUtil.convertTimeStampToLong(exam.getEndTime()));
      data.put("initial_bet_analysis", exam.isCompleted() ? getLearnerExamAnalysis(exam) : null);

    } else {
      data.put("initial_bet_score", null);
      data.put("initial_exam_id", null);
      data.put("initial_total_marks", null);
      data.put("initial_marks_scored", null);
      data.put("initial_bet_status", "NOT_ATTEMPTED");
      data.put("initial__completion_time", null);
      data.put("initial_bet_analysis", null);
    }
    if (finalBetOptionalExam.isPresent()) {
      var exam = finalBetOptionalExam.get();
      data.put(
          "final_bet_score",
          betReportCard.getBetScore((double) (exam.getMarksScored() / exam.getTotalMarks() * 100)));
      data.put("final_exam_id", exam.getId());
      data.put("final_total_marks", exam.getTotalMarks());
      data.put("final_marks_scored", exam.getMarksScored());
      data.put("final_bet_status", exam.isCompleted() ? "COMPLETED" : "IN_PROGRESS");
      data.put("final__completion_time", dateTimeUtil.convertTimeStampToLong(exam.getEndTime()));
      data.put("final_bet_analysis", exam.isCompleted() ? getLearnerExamAnalysis(exam) : null);
    } else {
      data.put("final_bet_score", null);
      data.put("final_exam_id", null);
      data.put("final_total_marks", null);
      data.put("final_marks_scored", null);
      data.put("final_bet_status", "NOT_ATTEMPTED");
      data.put("final__completion_time", null);
      data.put("final_bet_analysis", null);
    }
    userGameStatisticsRepository
        .findByUser(student.getUserInfo())
        .ifPresentOrElse(
            userGameStats -> {
              data.put("total_assessments", betPracticeExams);
              data.put("xp_points", userGameStats.getTotalXp());
              data.put(
                  "daily_streaks",
                  Constants.DECIMAL_FORMAT.format(userGameStats.getCurrentStreak()));
              data.put(
                  "xp_points__progression_percentage",
                  Constants.DECIMAL_FORMAT.format(
                      ((userGameStats.getCurrentStreak() - userGameStats.getLastWeekXp())
                              / userGameStats.getCurrentStreak())
                          * 100L));
            },
            () -> {
              data.put("xp_points", null);
              data.put("streaks_count", null);
              data.put("xp_progression_percentage", null);
            });

    genericMetricResponses.add(GenericMetricResponse.builder().summary(summary).data(data).build());
    return genericMetricResponses;
  }

  private List<GenericMetricResponse> getLearnerExamAnalysis(Exam exam) {

    List<GenericMetricResponse> genericMetricResponse = new LinkedList<>();
    BetReportDto.Body reportCard = null;
    if (exam.isCompleted()) {
      reportCard = betReportCard.buildBody(exam.getStudent(), exam.getScheduleTest(), null);
    }
    for (int i = 0; i < 4; i++) {
      genericMetricResponse.add(
          GenericMetricResponse.builder()
              .data(
                  betExamService.buildData(
                      exam.getScheduleTest().getTestDefinition().getTestName(), reportCard, i))
              .build());
    }
    return genericMetricResponse;
  }
}
