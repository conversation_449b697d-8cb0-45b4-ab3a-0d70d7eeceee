package com.wexl.betcorporate;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.sections.dto.BetSectionDto;
import com.wexl.sections.service.BetSectionService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/orgs/{orgSlug}")
@IsStudent
@RequiredArgsConstructor
public class BetCorporateController {
  private final BetCorporateService betCorporateService;
  private final BetSectionService betSectionService;

  @GetMapping("students/{authUserId}/bet-exams")
  public List<BetExam.Response> getBetExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return betCorporateService.getBetCorpExams(authUserId, orgSlug);
  }

  @GetMapping("/students/{authUserId}/speaking-exams")
  public List<BetExam.Response> getBetWritingExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return betCorporateService.getBetLabExamsByCategoryAndStudent(
        authUserId, orgSlug, TestCategory.SPEAK_LAB);
  }

  @GetMapping("/students/{authUserId}/writing-exams")
  public List<BetExam.Response> getBetSpeakingExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return betCorporateService.getBetLabExamsByCategoryAndStudent(
        authUserId, orgSlug, TestCategory.WRITE_LAB);
  }

  @GetMapping("/students/{authUserId}/placement-exams")
  public List<BetExam.Response> getBetPlacementExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return betCorporateService.getBetLabExamsByCategoryAndStudent(
        authUserId, orgSlug, TestCategory.PLACEMENT_LAB);
  }

  @PostMapping("/bet-corp:schedule")
  public BetExam.ScheduleResponse scheduleBet(@RequestBody BetExam.Request request) {
    return betCorporateService.scheduleBetExam(request);
  }

  @PostMapping("/students/{authUserId}/bet-exams")
  public ExamResponse startBetExam(@RequestBody @Valid BetExam.BetExamRequest betExamRequest) {
    return betSectionService.startBetExam(betExamRequest);
  }

  @PostMapping("/users/{authUserId}/bet-exams/{examId}:submit")
  public BetSectionDto.SubmitResponse submitBetExam(
      @PathVariable String orgSlug, @PathVariable Long examId) {
    return betSectionService.submitBetExam(examId, orgSlug);
  }
}
