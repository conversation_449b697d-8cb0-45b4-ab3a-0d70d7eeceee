package com.wexl.smartboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.school.dto.QuestionDto;
import lombok.Builder;

public record SmartBoardDto() {

  public record AttemptQuestionRequest(
      @JsonProperty("student_Id") String studentAuthId,
      @JsonProperty("student_answer_request")
          QuestionDto.BetAnswerValidateRequest betAnswerValidateRequest) {}

  @Builder
  public record SmartBoardElpQuestionResponse(QuestionDto.QuestionResponse questionResponse) {}

  @Builder
  public record StudentElpResponse(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_auth_id") String studentAuthId,
      @JsonProperty("question_uuid") String questionUuid,
      @JsonProperty("exam_id") Long examId) {}

  @Builder
  public record SmartBoardAttemptResponse(
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("student_answer_request")
          QuestionDto.ValidateAnswerResponse validateAnswerResponse) {}
}
