package com.wexl.metricshandler;

import com.wexl.betcorporate.BetCorporateService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequiredArgsConstructor
@Slf4j
public class LeanerDashBoard extends AbstractMetricHandler implements MetricHandler {
  @Autowired protected BetCorporateService betCorporateService;

  @Override
  public String name() {
    return "learner-dashboard";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String studentAuthId =
        Optional.ofNullable(genericMetricRequest.getInput())
            .map(input -> (String) input.getOrDefault("student_authId", null))
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "student authId is required"));
    return betCorporateService.getLearnerActivities(org, studentAuthId);
  }
}
