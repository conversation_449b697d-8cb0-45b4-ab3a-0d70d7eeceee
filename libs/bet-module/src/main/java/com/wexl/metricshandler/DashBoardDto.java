package com.wexl.metricshandler;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record DashBoardDto() {

  @Builder
  public record CourseContinuationResponse(
      @JsonProperty("total_lessons") Long totalLessons,
      @JsonProperty("current_lesson") Long completedLesson,
      @JsonProperty("course_title") String courseTitle,
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("section_inst_id") Long sectionInstId,
      @JsonProperty("section_unit_id") Long sectionUnitId,
      @JsonProperty("section_unit_inst_id") Long sectionUnitInstId,
      @JsonProperty("lesson_slug") String lessonSlug) {}
}
