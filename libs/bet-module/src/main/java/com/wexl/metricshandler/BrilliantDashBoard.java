package com.wexl.metricshandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.betcorporate.BetCorporateService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequiredArgsConstructor
@Slf4j
public class BrilliantDashBoard extends AbstractMetricHandler implements MetricHandler {
  @Autowired protected BetCorporateService betCorporateService;

  @Override
  public String name() {
    return "brilliant-dashboard";
  }

  String json =
      """
          [
            {
              "summary": {
                "categories_count": 4,
                "cerf_levels": 6,
                "total_learners": 700,
                "total_learners_progression_count": 420,
                "active_learners_progression_count": 336,
                "active_learners": 560,
                "attempted_percentage": 80.0,
                "inactive_learners": 140,
                "total_lessons": 1743
              },
              "data": {
                "initial_bet_average_score": 4.5,
                "initial_exam_attempted_count": 560,
                "initial_exam_not_attempted_count": 140,
                "initial_exam_progression_count": 0,
                "score_0_to_3_learners_count": 230,
                "score_3_to_6_learners_count": 260,
                "score_6_to_9_learners_count": 70,
                "practice_bet_average_score": 5.0,
                "practice_exam_progression_count": "40.00",
                "daily_streaks": "150",
                "daily_streaks_progression_percentage": "90",
                "xp_points_learners_average": 3000,
                "xp_points__progression_percentage": "85.0",
                "final_bet_average_score": 5.5,
                "final_exam_attempted_count": 600,
                "final_exam_not_attempted_count": 100,
                "final_exam_progression_count": 300,
                "final_0_to_3_learners_count": 100,
                "final_3_to_6_learners_count": 300,
                "final_6_to_9_learners_count": 200
              }
            }
          ]

          """;

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String orgSlug =
        Optional.ofNullable(genericMetricRequest.getInput())
            .map(input -> (String) input.getOrDefault("org_slug", org))
            .orElse(org);
    if ("wex877390".equals(orgSlug)) {
      ObjectMapper objectMapper = new ObjectMapper();
      try {
        var result = objectMapper.readValue(json, GenericMetricResponse[].class);
        return List.of(result);
      } catch (JsonProcessingException e) {
        log.error("Json error", e);
      }
    }
    return betCorporateService.getCorporateMetrics(orgSlug);
  }
}
