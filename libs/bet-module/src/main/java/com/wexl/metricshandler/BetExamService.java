package com.wexl.metricshandler;

import com.wexl.reportcard.BetReportCard;
import com.wexl.reportcard.dto.BetReportDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.ImprovementLevel;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import java.util.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class BetExamService {
  private final BetReportCard betReportCard;
  private final UserRepository userRepository;
  private final ScheduleTestService scheduleTestService;
  private static final String SECTION_NAME = "sectionName";
  private static final String MARKS = "marks";
  private static final String TOTAL_QUESTIONS = "total_questions";
  private static final String IMPROVEMENT = "improvement";
  private static final String PREVIOUS_MARKS = "previous_marks";
  private static final String PREVIOUS_TOTAL_QUESTIONS = "previous_total_questions";
  private static final String TOTAL_SCORE = "total_score";
  private static final String PROFICIENCY = "proficiency";
  private static final String PERCENTAGE = "percentage";

  public List<GenericMetricResponse> getBetExamAnalysis(String authUserId, String testScheduleId) {
    var user =
        userRepository
            .findByAuthUserId(authUserId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var testSchedule = scheduleTestService.validateTestSchedule(Long.valueOf(testScheduleId));
    BetReportDto.Body reportCard = null;
    var testScheduleStudent =
        testSchedule.getScheduleTestStudent().stream()
            .filter(tss -> tss.getStudent().equals(user))
            .findFirst();
    if (testScheduleStudent.isPresent()
        && Objects.equals(testScheduleStudent.get().getStatus(), "COMPLETED")) {
      reportCard = betReportCard.buildBody(user.getStudentInfo(), testSchedule, null);
    }
    List<GenericMetricResponse> genericMetricResponses = new LinkedList<>();
    for (int i = 0; i < 4; i++) {
      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .data(buildData(testSchedule.getTestDefinition().getTestName(), reportCard, i))
              .build());
    }
    return genericMetricResponses;
  }

  public Map<String, Object> buildData(String testName, BetReportDto.Body reportCard, int i) {
    Map<String, Object> map = new LinkedHashMap<>();
    if (Objects.isNull(reportCard)) {
      return map;
    }
    if (i == 0) {
      map.put("test_name", testName);
      map.put(TOTAL_SCORE, reportCard.scoreValue());
      map.put(PROFICIENCY, reportCard.proficiencyValue());
      map.put(SECTION_NAME, reportCard.section1Name());
      map.put(MARKS, reportCard.section1Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section1Questions());
      return map;
    } else if (i == 1) {
      map.put(SECTION_NAME, reportCard.section2Name());
      map.put(MARKS, reportCard.section2Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section2Questions());
      return map;
    } else if (i == 2) {
      map.put(SECTION_NAME, reportCard.section3Name());
      map.put(MARKS, reportCard.section3Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section3Questions());
      return map;
    } else {
      map.put(SECTION_NAME, reportCard.section4Name());
      map.put(MARKS, reportCard.section4Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section4Questions());
      return map;
    }
  }

  public List<GenericMetricResponse> buildPreviousAndCurrentResponse(
      List<GenericMetricResponse> previousGenericMetricResponses,
      List<GenericMetricResponse> currentMetricResponses) {
    List<GenericMetricResponse> result = new ArrayList<>();
    for (int i = 0; i < 4; i++) {
      Map<String, Object> map = new LinkedHashMap<>();
      var previous = previousGenericMetricResponses.get(i);
      var current = currentMetricResponses.get(i);
      if (i == 0) {
        map.put("previous_test_name", previous.getData().get("test_name"));
        map.put("test_name", current.getData().get("test_name"));
        mapResponse(map, previous, current);
      } else if (i == 1) {
        mapResponse(map, previous, current);
      } else if (i == 2) {
        mapResponse(map, previous, current);
      } else {
        mapResponse(map, previous, current);
      }
      var toAdd = new GenericMetricResponse();
      toAdd.setData(map);
      result.add(toAdd);
    }
    return result;
  }

  private void mapResponse(
      Map<String, Object> map, GenericMetricResponse previous, GenericMetricResponse current) {
    map.put(SECTION_NAME, current.getData().get(SECTION_NAME));
    map.put(PREVIOUS_MARKS, previous.getData().getOrDefault(MARKS, 0.0));
    map.put(MARKS, current.getData().get(MARKS));
    map.put(PREVIOUS_TOTAL_QUESTIONS, previous.getData().getOrDefault(TOTAL_QUESTIONS, 0.0));
    map.put(TOTAL_QUESTIONS, current.getData().get(TOTAL_QUESTIONS));
    var previousDataMarks = previous.getData().get(MARKS);
    var previousMarks =
        Objects.nonNull(previousDataMarks) ? Double.parseDouble((String) previousDataMarks) : 0.0;
    var currentDataMarks = previous.getData().get(MARKS);
    var currentMarks =
        Objects.nonNull(currentDataMarks) ? Double.parseDouble((String) currentDataMarks) : 0.0;
    if (currentMarks - previousMarks > 1.0D) {
      map.put(IMPROVEMENT, ImprovementLevel.IMPROVED);
    } else if (previousMarks - currentMarks > 1.0D) {
      map.put(IMPROVEMENT, ImprovementLevel.DECLINED);
    } else if (Math.abs(currentMarks - previousMarks) <= 1.0D) {
      map.put(IMPROVEMENT, ImprovementLevel.SAME);
    }
  }

  public String getPreviousTestScheduleId(String authUserId, String testScheduleId) {
    var user =
        userRepository
            .findByAuthUserId(authUserId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var testSchedule = scheduleTestService.validateTestSchedule(Long.valueOf(testScheduleId));
    var testScheduleStudent =
        testSchedule.getScheduleTestStudent().stream()
            .filter(tss -> tss.getStudent().equals(user))
            .findFirst();
    if (testScheduleStudent.isPresent()
        && Objects.equals(testScheduleStudent.get().getStatus(), "COMPLETED")) {
      Long previousTestScheduleId =
          scheduleTestService.getPreviousTestScheduleId(
              user.getId(), testScheduleStudent.get().getCreatedAt());
      return previousTestScheduleId == null ? null : String.valueOf(previousTestScheduleId);
    }
    return null;
  }
}
