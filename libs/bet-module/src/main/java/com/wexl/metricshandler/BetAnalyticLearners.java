package com.wexl.metricshandler;

import com.wexl.betcorporate.BetCorporateService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequiredArgsConstructor
public class BetAnalyticLearners extends AbstractMetricHandler implements MetricHandler {
  @Autowired protected BetCorporateService betCorporateService;

  @Override
  public String name() {
    return "analytic-learners";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String orgSlug =
        Optional.ofNullable(genericMetricRequest.getInput())
            .map(input -> (String) input.getOrDefault("org_slug", org))
            .orElse(org);
    return betCorporateService.getAnalyticLearners(orgSlug);
  }
}
