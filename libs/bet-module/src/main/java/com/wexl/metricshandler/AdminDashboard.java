package com.wexl.metricshandler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import com.wexl.sections.service.BetDashBoardService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AdminDashboard extends AbstractMetricHandler implements MetricHandler {

  private final BetDashBoardService dashBoardService;

  @Override
  public String name() {
    return "admin-dashboard";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    return dashBoardService.getAdminDashboard(org);
  }
}
