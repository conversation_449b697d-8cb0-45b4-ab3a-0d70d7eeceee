package com.wexl.admissiontests.controller;

import com.wexl.admissiontests.dto.AdmissionTestDto;
import com.wexl.admissiontests.service.AdmissionTestService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.model.Grade;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class AdmissionTestController {

  private final AdmissionTestService admissionTestService;

  @PostMapping("/students/{studentAuthId}/admission-tests")
  public AdmissionTestDto.Response createTest(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestBody AdmissionTestDto.Request request) {
    return admissionTestService.createTest(orgSlug, request, studentAuthId);
  }

  @GetMapping("/admission-tests/grades")
  public List<Grade> getGradesByTestDefinition() {
    return admissionTestService.getGradesByTestDefinition();
  }

  @IsOrgAdmin
  @GetMapping("/admission-tests")
  public List<AdmissionTestDto.AdmissionTestsResponse> getAdmissionTests(
      @PathVariable String orgSlug) {
    return admissionTestService.getAdmissionTests(orgSlug);
  }
}
