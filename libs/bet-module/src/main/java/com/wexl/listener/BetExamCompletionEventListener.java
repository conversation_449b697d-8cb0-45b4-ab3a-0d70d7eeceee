package com.wexl.listener;

import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.publisher.ExamCompletionEvent;
import com.wexl.retail.util.Constants;
import com.wexl.retail.v2.service.ScheduleTestStudentService;
import com.wexl.sections.service.BetSectionService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class BetExamCompletionEventListener implements ApplicationListener<ExamCompletionEvent> {

  private final ScheduleTestStudentService scheduleTestStudentService;

  private final BetSectionService betSectionService;

  @Override
  public void onApplicationEvent(ExamCompletionEvent examCompletionEvent) {
    Object source = examCompletionEvent.getSource();
    if (source instanceof Exam exam && Objects.equals(Constants.MOCK_TEST, exam.getExamType())) {
      scheduleTestStudentService.generateBetReportCardAsync(exam.getId());
      var user = exam.getStudent().getUserInfo();
      // betSectionService.unlockModules(user.getAuthUserId(), user.getOrganization());
    }
  }
}
