package com.wexl.speech.processor.speechace;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.speech.processor.speechace.dto.SpeechAceResponse;
import java.util.List;
import org.junit.jupiter.api.Test;

class SpeechAceTransformerTest {

  private final SpeechAceTransformer transformer = new SpeechAceTransformer();

  private String requestBody =
      """
            {"speech_score":{"transcript":"Hello team, good morning to everyone. I'm <PERSON><PERSON><PERSON><PERSON> and I'm coming from Hyderabad coming to my hobbies.","word_score_list":[{"word":"Hello","quality_score":90.0,"phone_score_list":[{"phone":"hh","stress_level":null,"quality_score":99.0,"sound_most_like":"hh","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[50,62]},{"phone":"ah","stress_level":"0","quality_score":62.833333333333336,"sound_most_like":"iy","word_extent":[1,2],"stress_score":100,"predicted_stress_level":0,"extent":[62,68]},{"phone":"l","stress_level":null,"quality_score":99.66666666666667,"sound_most_like":"l","word_extent":[3,4],"stress_score":null,"predicted_stress_level":null,"extent":[68,77]},{"phone":"ow","stress_level":"1","quality_score":98.33333333333333,"sound_most_like":"ow","word_extent":[4,5],"stress_score":100,"predicted_stress_level":1,"extent":[77,86]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":0,"letters":"hel","quality_score":81.0,"stress_score":100.0,"extent":[50,68],"predicted_stress_level":0},{"phone_count":2,"stress_level":1,"letters":"lo","quality_score":99.0,"stress_score":100.0,"extent":[68,86],"predicted_stress_level":1}]},{"word":"team","quality_score":96.0,"phone_score_list":[{"phone":"t","stress_level":null,"quality_score":93.0,"sound_most_like":"t","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[86,98]},{"phone":"iy","stress_level":"1","quality_score":95.0,"sound_most_like":"iy","word_extent":[1,3],"stress_score":100,"predicted_stress_level":1,"extent":[98,107]},{"phone":"m","stress_level":null,"quality_score":100.0,"sound_most_like":"m","word_extent":[3,4],"stress_score":null,"predicted_stress_level":null,"extent":[107,116]}],"ending_punctuation":",","syllable_score_list":[{"phone_count":3,"stress_level":1,"letters":"team","quality_score":96.0,"stress_score":100.0,"extent":[86,116],"predicted_stress_level":1}]},{"word":"good","quality_score":78.0,"phone_score_list":[{"phone":"g","stress_level":null,"quality_score":35.0,"sound_most_like":"p","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[119,125]},{"phone":"uh","stress_level":"1","quality_score":100.0,"sound_most_like":"uh","word_extent":[1,3],"stress_score":100,"predicted_stress_level":1,"extent":[125,131]},{"phone":"d","stress_level":null,"quality_score":99.5,"sound_most_like":"d","word_extent":[3,4],"stress_score":null,"predicted_stress_level":null,"extent":[131,137]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":3,"stress_level":1,"letters":"good","quality_score":78.0,"stress_score":100.0,"extent":[119,137],"predicted_stress_level":1}]},{"word":"morning","quality_score":87.0,"phone_score_list":[{"phone":"m","stress_level":null,"quality_score":99.66666666666667,"sound_most_like":"m","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[137,146]},{"phone":"ao","stress_level":"1","quality_score":97.33333333333333,"sound_most_like":"ao","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[146,155]},{"phone":"r","stress_level":null,"quality_score":35.0,"sound_most_like":"n","word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[155,156]},{"phone":"n","stress_level":null,"quality_score":100.0,"sound_most_like":"n","word_extent":[3,4],"stress_score":null,"predicted_stress_level":null,"extent":[156,161]},{"phone":"ih","stress_level":"0","quality_score":97.0,"sound_most_like":"ih","word_extent":[4,5],"stress_score":100,"predicted_stress_level":0,"extent":[161,167]},{"phone":"ng","stress_level":null,"quality_score":95.5,"sound_most_like":"ng","word_extent":[5,7],"stress_score":null,"predicted_stress_level":null,"extent":[167,173]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":4,"stress_level":1,"letters":"morn","quality_score":83.0,"stress_score":100.0,"extent":[137,161],"predicted_stress_level":1},{"phone_count":2,"stress_level":0,"letters":"ing","quality_score":96.0,"stress_score":100.0,"extent":[161,173],"predicted_stress_level":0}]},{"word":"to","quality_score":96.0,"phone_score_list":[{"phone":"t","stress_level":null,"quality_score":93.0,"sound_most_like":"t","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[173,179]},{"phone":"uw","stress_level":"1","quality_score":98.0,"sound_most_like":"uw","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[179,191]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"to","quality_score":96.0,"stress_score":100.0,"extent":[173,191],"predicted_stress_level":1}]},{"word":"everyone","quality_score":99.0,"phone_score_list":[{"phone":"eh","stress_level":"1","quality_score":99.75,"sound_most_like":"eh","word_extent":[0,1],"stress_score":100,"predicted_stress_level":1,"extent":[194,206]},{"phone":"v","stress_level":null,"quality_score":100.0,"sound_most_like":"v","word_extent":[1,3],"stress_score":null,"predicted_stress_level":null,"extent":[206,212]},{"phone":"r","stress_level":null,"quality_score":99.5,"sound_most_like":"r","word_extent":[3,4],"stress_score":null,"predicted_stress_level":null,"extent":[212,218]},{"phone":"iy","stress_level":"0","quality_score":99.0,"sound_most_like":"iy","word_extent":[4,5],"stress_score":100,"predicted_stress_level":0,"extent":[218,221]},{"phone":"w","stress_level":null,"quality_score":99.5,"sound_most_like":"w","word_extent":[5,6],"stress_score":null,"predicted_stress_level":null,"extent":[221,227]},{"phone":"ah","stress_level":"2","quality_score":93.0,"sound_most_like":"ah","word_extent":[5,6],"stress_score":50,"predicted_stress_level":0,"extent":[227,233]},{"phone":"n","stress_level":null,"quality_score":100.0,"sound_most_like":"n","word_extent":[6,8],"stress_score":null,"predicted_stress_level":null,"extent":[233,242]}],"ending_punctuation":".","syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"eve","quality_score":100.0,"stress_score":100.0,"extent":[194,212],"predicted_stress_level":1},{"phone_count":2,"stress_level":0,"letters":"ry","quality_score":99.0,"stress_score":100.0,"extent":[212,221],"predicted_stress_level":0},{"phone_count":3,"stress_level":2,"letters":"one","quality_score":98.0,"stress_score":50.0,"extent":[221,242],"predicted_stress_level":0}]},{"word":"I'm","quality_score":93.0,"phone_score_list":[{"phone":"ay","stress_level":"1","quality_score":86.6,"sound_most_like":"ay","word_extent":[0,1],"stress_score":100,"predicted_stress_level":1,"extent":[251,266]},{"phone":"m","stress_level":null,"quality_score":100.0,"sound_most_like":"m","word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[266,275]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"i'm","quality_score":93.0,"stress_score":100.0,"extent":[251,275],"predicted_stress_level":1}]},{"word":"Pujita","quality_score":95.0,"phone_score_list":[{"phone":"p","stress_level":null,"quality_score":98.0,"sound_most_like":"p","word_extent":null,"stress_score":null,"predicted_stress_level":null,"extent":[275,284]},{"phone":"uw","stress_level":"0","quality_score":96.0,"sound_most_like":"uw","word_extent":null,"stress_score":90,"predicted_stress_level":1,"extent":[284,290]},{"phone":"jh","stress_level":null,"quality_score":96.0,"sound_most_like":"jh","word_extent":null,"stress_score":null,"predicted_stress_level":null,"extent":[290,302]},{"phone":"iy","stress_level":"1","quality_score":92.5,"sound_most_like":"iy","word_extent":null,"stress_score":98,"predicted_stress_level":0,"extent":[302,308]},{"phone":"t","stress_level":null,"quality_score":89.33333333333333,"sound_most_like":"t","word_extent":null,"stress_score":null,"predicted_stress_level":null,"extent":[308,317]},{"phone":"ah","stress_level":"0","quality_score":99.2,"sound_most_like":"ah","word_extent":null,"stress_score":100,"predicted_stress_level":0,"extent":[317,332]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":0,"letters":"pu","quality_score":97.0,"stress_score":90.0,"extent":[275,290],"predicted_stress_level":1},{"phone_count":2,"stress_level":1,"letters":"ji","quality_score":94.0,"stress_score":98.0,"extent":[290,308],"predicted_stress_level":0},{"phone_count":2,"stress_level":0,"letters":"ta","quality_score":94.0,"stress_score":100.0,"extent":[308,332],"predicted_stress_level":0}]},{"word":"and","quality_score":66.0,"phone_score_list":[{"phone":"ae","stress_level":"1","quality_score":99.33333333333333,"sound_most_like":"ae","word_extent":[0,1],"stress_score":100,"predicted_stress_level":1,"extent":[335,344]},{"phone":"n","stress_level":null,"quality_score":99.33333333333333,"sound_most_like":"n","word_extent":[1,2],"stress_score":null,"predicted_stress_level":null,"extent":[344,353]},{"phone":"d","stress_level":null,"quality_score":0.0,"sound_most_like":null,"word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[353,353]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":3,"stress_level":1,"letters":"and","quality_score":66.0,"stress_score":100.0,"extent":[335,353],"predicted_stress_level":1}]},{"word":"I'm","quality_score":99.0,"phone_score_list":[{"phone":"ay","stress_level":"1","quality_score":98.0,"sound_most_like":"ay","word_extent":[0,1],"stress_score":100,"predicted_stress_level":1,"extent":[356,362]},{"phone":"m","stress_level":null,"quality_score":100.0,"sound_most_like":"m","word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[362,371]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"i'm","quality_score":99.0,"stress_score":100.0,"extent":[356,371],"predicted_stress_level":1}]},{"word":"coming","quality_score":93.0,"phone_score_list":[{"phone":"k","stress_level":null,"quality_score":100.0,"sound_most_like":"k","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[371,380]},{"phone":"ah","stress_level":"1","quality_score":100.0,"sound_most_like":"ah","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[380,386]},{"phone":"m","stress_level":null,"quality_score":99.5,"sound_most_like":"m","word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[386,392]},{"phone":"ih","stress_level":"0","quality_score":98.33333333333333,"sound_most_like":"ih","word_extent":[3,4],"stress_score":100,"predicted_stress_level":0,"extent":[392,401]},{"phone":"ng","stress_level":null,"quality_score":68.83333333333333,"sound_most_like":"n","word_extent":[4,6],"stress_score":null,"predicted_stress_level":null,"extent":[401,407]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":3,"stress_level":1,"letters":"com","quality_score":100.0,"stress_score":100.0,"extent":[371,392],"predicted_stress_level":1},{"phone_count":2,"stress_level":0,"letters":"ing","quality_score":84.0,"stress_score":100.0,"extent":[392,407],"predicted_stress_level":0}]},{"word":"from","quality_score":100.0,"phone_score_list":[{"phone":"f","stress_level":null,"quality_score":98.66666666666667,"sound_most_like":"f","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[407,416]},{"phone":"r","stress_level":null,"quality_score":100.0,"sound_most_like":"r","word_extent":[1,2],"stress_score":null,"predicted_stress_level":null,"extent":[416,422]},{"phone":"ah","stress_level":"1","quality_score":100.0,"sound_most_like":"ah","word_extent":[2,3],"stress_score":100,"predicted_stress_level":1,"extent":[422,425]},{"phone":"m","stress_level":null,"quality_score":100.0,"sound_most_like":"m","word_extent":[3,4],"stress_score":null,"predicted_stress_level":null,"extent":[425,431]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":4,"stress_level":1,"letters":"from","quality_score":100.0,"stress_score":100.0,"extent":[407,431],"predicted_stress_level":1}]},{"word":"Hyderabad","quality_score":64.0,"phone_score_list":[{"phone":"hh","stress_level":null,"quality_score":100.0,"sound_most_like":"hh","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[431,440]},{"phone":"ay","stress_level":"1","quality_score":99.0,"sound_most_like":"ay","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[440,449]},{"phone":"d","stress_level":null,"quality_score":79.25,"sound_most_like":"r","word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[449,461]},{"phone":"er","stress_level":"0","quality_score":66.75,"sound_most_like":"b","word_extent":[3,5],"stress_score":100,"predicted_stress_level":0,"extent":[461,465]},{"phone":"ah","stress_level":"0","quality_score":0.0,"sound_most_like":"b","word_extent":[5,6],"stress_score":100,"predicted_stress_level":0,"extent":[465,466]},{"phone":"b","stress_level":null,"quality_score":90.25,"sound_most_like":"b","word_extent":[6,7],"stress_score":null,"predicted_stress_level":null,"extent":[466,470]},{"phone":"ae","stress_level":"2","quality_score":41.666666666666664,"sound_most_like":"ah","word_extent":[7,8],"stress_score":96,"predicted_stress_level":0,"extent":[470,476]},{"phone":"d","stress_level":null,"quality_score":38.333333333333336,"sound_most_like":"n","word_extent":[8,9],"stress_score":null,"predicted_stress_level":null,"extent":[476,482]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"hy","quality_score":100.0,"stress_score":100.0,"extent":[431,449],"predicted_stress_level":1},{"phone_count":2,"stress_level":0,"letters":"der","quality_score":73.0,"stress_score":100.0,"extent":[449,465],"predicted_stress_level":0},{"phone_count":1,"stress_level":0,"letters":"a","quality_score":0.0,"stress_score":100.0,"extent":[465,466],"predicted_stress_level":0},{"phone_count":3,"stress_level":2,"letters":"bad","quality_score":57.0,"stress_score":96.0,"extent":[466,482],"predicted_stress_level":0}]},{"word":"coming","quality_score":100.0,"phone_score_list":[{"phone":"k","stress_level":null,"quality_score":100.0,"sound_most_like":"k","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[506,515]},{"phone":"ah","stress_level":"1","quality_score":100.0,"sound_most_like":"ah","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[515,524]},{"phone":"m","stress_level":null,"quality_score":100.0,"sound_most_like":"m","word_extent":[2,3],"stress_score":null,"predicted_stress_level":null,"extent":[524,527]},{"phone":"ih","stress_level":"0","quality_score":99.66666666666667,"sound_most_like":"ih","word_extent":[3,4],"stress_score":100,"predicted_stress_level":0,"extent":[527,536]},{"phone":"ng","stress_level":null,"quality_score":100.0,"sound_most_like":"ng","word_extent":[4,6],"stress_score":null,"predicted_stress_level":null,"extent":[536,542]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":3,"stress_level":1,"letters":"com","quality_score":100.0,"stress_score":100.0,"extent":[506,527],"predicted_stress_level":1},{"phone_count":2,"stress_level":0,"letters":"ing","quality_score":100.0,"stress_score":100.0,"extent":[527,542],"predicted_stress_level":0}]},{"word":"to","quality_score":87.0,"phone_score_list":[{"phone":"t","stress_level":null,"quality_score":100.0,"sound_most_like":"t","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[542,548]},{"phone":"uw","stress_level":"1","quality_score":74.625,"sound_most_like":"m","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[548,556]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"to","quality_score":87.0,"stress_score":100.0,"extent":[542,556],"predicted_stress_level":1}]},{"word":"my","quality_score":100.0,"phone_score_list":[{"phone":"m","stress_level":null,"quality_score":99.66666666666667,"sound_most_like":"m","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[557,566]},{"phone":"ay","stress_level":"1","quality_score":100.0,"sound_most_like":"ay","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[566,572]}],"ending_punctuation":null,"syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"my","quality_score":100.0,"stress_score":100.0,"extent":[557,572],"predicted_stress_level":1}]},{"word":"hobbies","quality_score":82.0,"phone_score_list":[{"phone":"hh","stress_level":null,"quality_score":100.0,"sound_most_like":"hh","word_extent":[0,1],"stress_score":null,"predicted_stress_level":null,"extent":[572,581]},{"phone":"aa","stress_level":"1","quality_score":50.66666666666666,"sound_most_like":"ao","word_extent":[1,2],"stress_score":100,"predicted_stress_level":1,"extent":[581,590]},{"phone":"b","stress_level":null,"quality_score":74.0,"sound_most_like":"r","word_extent":[2,4],"stress_score":null,"predicted_stress_level":null,"extent":[590,605]},{"phone":"iy","stress_level":"0","quality_score":93.5,"sound_most_like":"iy","word_extent":[4,6],"stress_score":100,"predicted_stress_level":0,"extent":[605,617]},{"phone":"z","stress_level":null,"quality_score":92.0,"sound_most_like":"z","word_extent":[6,7],"stress_score":null,"predicted_stress_level":null,"extent":[617,650]}],"ending_punctuation":".","syllable_score_list":[{"phone_count":2,"stress_level":1,"letters":"ho","quality_score":75.0,"stress_score":100.0,"extent":[572,590],"predicted_stress_level":1},{"phone_count":3,"stress_level":0,"letters":"bbies","quality_score":86.0,"stress_score":100.0,"extent":[590,650],"predicted_stress_level":0}]}],"relevance":{"class":"TRUE"},"ielts_score":{"pronunciation":8.0,"fluency":8.5,"grammar":5.0,"coherence":5.5,"vocab":5.5,"overall":0.0},"speechace_score":{"pronunciation":89.0,"fluency":93.0,"grammar":57.0,"coherence":62.0,"vocab":61.0,"overall":0.0},"pte_score":{"pronunciation":85.0,"fluency":90.0,"grammar":35.0,"coherence":43.0,"vocab":42.0,"overall":10.0},"toeic_score":{"pronunciation":180.0,"fluency":190.0,"grammar":90.0,"coherence":110.0,"vocab":110.0,"overall":0.0},"grammar":{"overall_metrics":{"length":{"score":1,"level":"low","message":"Your response is too short to demonstrate the necessary grammatical range. You should aim for a minimum of 5 sentences or 60 words.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"lexical_diversity":{"score":1,"level":"low","message":"Your response lacks variation in syntactic structures. You should use diverse verbs and adjective/adverbial modifiers.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"lexical_density":null,"basic_connectives":null,"causal_connectives":null,"negative_connectives":null,"grammatical_accuracy":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"pronoun_density":null,"adverb_diversity":null,"verb_diversity":null,"grammatical_range":{"score":2,"level":"low","message":"Your response lacks grammatical range. You should demonstrate variation in phrasal and clausal structures and verb-argument constructions.","examples":null,"noun_phrase_complexity":{"score":4,"level":"mid","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"noun_phrase_variation":{"score":1,"level":"low","message":"Your response lacks noun phrase variation. You should vary the structure of noun phrases particularly in terms of the numbers and types of modifiers used.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"verb_construction_variation":{"score":1,"level":"low","message":"Your response lacks verb construction variation. In addition to verbs with simple fixed structures, you should use verbs that may require different numbers and types of structural elements.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"adverb_modifier_variation":{"score":1,"level":"low","message":"Your response lacks adverb modifier variation. You should use more diverse types of adverbs or adverb phrases to modify clauses, verbs, and adjectives to provide richer information.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null}},"word_sophistication":null,"word_specificity":null,"academic_language_use":null,"collocation_commonality":null,"idiomaticity":null},"errors":[]},"vocab":{"overall_metrics":{"length":null,"lexical_diversity":{"score":4,"level":"mid","message":"Your response has less word diversity than most advanced speakers. To improve, you should reduce repetition and use more diverse words.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"lexical_density":null,"basic_connectives":null,"causal_connectives":null,"negative_connectives":null,"grammatical_accuracy":null,"pronoun_density":null,"adverb_diversity":null,"verb_diversity":null,"grammatical_range":null,"word_sophistication":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"word_specificity":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"academic_language_use":{"score":1,"level":"low","message":"Your response is low on use of academic words. You should learn and use some academic language in your responses to improve.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"collocation_commonality":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"idiomaticity":{"score":3,"level":"low","message":"Your use of word combinations is unusual and typical of adaptation from another language. You should study and use combinations such as adjective-noun and adverb-verb which are associated with each other and found in idiomatic language. ","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null}},"errors":null},"coherence":{"overall_metrics":{"length":null,"lexical_diversity":null,"lexical_density":{"score":5,"level":"mid","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"basic_connectives":{"score":6,"level":"mid","message":null,"examples":["and"],"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"causal_connectives":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"negative_connectives":{"score":1,"level":"low","message":"Your response is low or lacking in use of negative connectives. You should practice negative connectives such as admittedly, alternatively, nevertheless, conversely to signal contrast where appropriate.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"grammatical_accuracy":null,"pronoun_density":{"score":1,"level":"low","message":"Your response is low on pronouns. You should practice replacing some repeated nouns with third person pronouns where appropriate.","examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"adverb_diversity":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"verb_diversity":{"score":10,"level":"high","message":null,"examples":null,"noun_phrase_complexity":null,"noun_phrase_variation":null,"verb_construction_variation":null,"adverb_modifier_variation":null},"grammatical_range":null,"word_sophistication":null,"word_specificity":null,"academic_language_use":null,"collocation_commonality":null,"idiomaticity":null},"errors":null},"fluency":{"segment_metrics_list":[{"segment":[0,6],"duration":1.965,"articulation_length":1.86,"syllable_count":10,"correct_syllable_count":9,"correct_word_count":5,"word_count":6,"speech_rate":5.0890584,"articulation_rate":5.376344,"syllable_correct_per_minute":274.80917,"word_correct_per_minute":152.67175,"all_pause_count":3,"all_pause_duration":0.105,"mean_length_run":0.62,"max_length_run":0.72,"all_pause_list":[[116,119],[191,194],[242,246.5]],"ielts_score":{"pronunciation":8.5,"fluency":8.0,"grammar":4.5,"coherence":4.0,"vocab":4.0,"overall":null},"pte_score":{"pronunciation":89.0,"fluency":83.0,"grammar":23.0,"coherence":20.0,"vocab":20.0,"overall":null},"speechace_score":{"pronunciation":92.0,"fluency":88.0,"grammar":48.0,"coherence":44.0,"vocab":44.0,"overall":null},"toeic_score":{"pronunciation":190.0,"fluency":180.0,"grammar":80.0,"coherence":50.0,"vocab":50.0,"overall":null},"cefr_score":{"pronunciation":"C2","fluency":"C1+","grammar":"A1+","coherence":"A1","vocab":"A1","overall":null}},{"segment":[6,17],"duration":4.035,"articulation_length":3.68,"syllable_count":19,"correct_syllable_count":14,"correct_word_count":9,"word_count":11,"speech_rate":4.708798,"articulation_rate":5.1630435,"syllable_correct_per_minute":208.17844,"word_correct_per_minute":133.829,"all_pause_count":5,"all_pause_duration":0.355,"mean_length_run":0.736,"max_length_run":1.26,"all_pause_list":[[246.5,251],[332,335],[353,356],[482,506],[556,557]],"ielts_score":{"pronunciation":8.0,"fluency":8.0,"grammar":5.0,"coherence":4.5,"vocab":5.0,"overall":null},"pte_score":{"pronunciation":82.0,"fluency":86.0,"grammar":35.0,"coherence":23.0,"vocab":30.0,"overall":null},"speechace_score":{"pronunciation":87.0,"fluency":90.0,"grammar":57.0,"coherence":48.0,"vocab":53.0,"overall":null},"toeic_score":{"pronunciation":180.0,"fluency":180.0,"grammar":90.0,"coherence":80.0,"vocab":90.0,"overall":null},"cefr_score":{"pronunciation":"C1+","fluency":"C1+","grammar":"A2","coherence":"A1+","vocab":"A2","overall":null}}],"overall_metrics":{"segment":[0,17],"duration":6.0,"articulation_length":5.54,"syllable_count":29,"correct_syllable_count":23,"correct_word_count":14,"word_count":17,"speech_rate":4.8333335,"articulation_rate":5.234657,"syllable_correct_per_minute":230.0,"word_correct_per_minute":140.0,"all_pause_count":7,"all_pause_duration":0.46,"mean_length_run":0.6925,"max_length_run":1.26,"all_pause_list":[[116,119],[191,194],[242,251],[332,335],[353,356],[482,506],[556,557]],"ielts_score":null,"pte_score":null,"speechace_score":null,"toeic_score":null,"cefr_score":null},"fluency_version":"0.7","ielts_subscore_version":"0.4"},"asr_version":"0.4","cefr_score":{"pronunciation":"C1+","fluency":"C2","grammar":"A2","coherence":"A2+","vocab":"A2+","overall":"A0"}},"words":null,"assessment":null}
            """;

  @Test
  void testTransformFromSpeechAceSpeechScore() throws JsonProcessingException {
    ObjectMapper objectMapper = new ObjectMapper();
    SpeechAceResponse.Response source =
        objectMapper.readValue(requestBody, SpeechAceResponse.Response.class);
    // Transform the response
    SpeechEvaluation.SpeechScore result =
        transformer.transformFromSpeechAceSpeechScore(source.speechScore());

    // Verify basic transformation
    assertNotNull(result);
    assertEquals(
        "Hello team, good morning to everyone. I'm Pujita and I'm coming from Hyderabad coming to my hobbies.",
        result.transcript());

    // Verify word scores
    assertNotNull(result.wordScoreList());
    assertEquals(17, result.wordScoreList().size());

    // Check first word details
    SpeechEvaluation.WordScore firstWord = result.wordScoreList().getFirst();
    assertEquals("Hello", firstWord.word());
    assertEquals(90.0, firstWord.qualityScore());
    assertNull(firstWord.endingPunctuation());
    assertEquals(List.of(50L, 86L), firstWord.wordExtent());

    // Check second word details
    SpeechEvaluation.WordScore secondWord = result.wordScoreList().get(1);
    assertEquals("team", secondWord.word());
    assertEquals(96.0, secondWord.qualityScore());
    assertEquals(",", secondWord.endingPunctuation());

    // Verify pronunciation scores
    assertNotNull(result.ieltsScore());
    assertEquals(8.0F, result.ieltsScore().pronunciation());
    assertEquals(8.5F, result.ieltsScore().fluency());
    assertEquals(5.0F, result.ieltsScore().grammar());
    assertEquals(5.5F, result.ieltsScore().coherence());
    assertEquals(5.5F, result.ieltsScore().vocab());
    assertEquals(0.0F, result.ieltsScore().overall());

    // Verify other scoring systems
    assertNotNull(result.speechAceScore());
    assertEquals(89.0F, result.speechAceScore().pronunciation());
    assertEquals(90.0F, result.speechAceScore().fluency());

    assertNotNull(result.pteScore());
    assertEquals(85.0F, result.pteScore().pronunciation());
    assertEquals(10.0F, result.pteScore().overall());

    // Verify grammar analysis
    assertNotNull(result.grammar());
    assertNotNull(result.grammar().overallMetrics());
    assertEquals(10, result.grammar().overallMetrics().grammaticalAccuracy().score());
    assertEquals("high", result.grammar().overallMetrics().grammaticalAccuracy().level());

    // Check grammar's nested scores
    assertNotNull(result.grammar().overallMetrics().grammaticalRange());
    assertEquals(
        4, result.grammar().overallMetrics().grammaticalRange().nounPhraseComplexity().score());
    assertEquals(
        "mid", result.grammar().overallMetrics().grammaticalRange().nounPhraseComplexity().level());

    // Verify vocabulary metrics
    assertNotNull(result.vocab());
    assertNotNull(result.vocab().overallMetrics());
    assertEquals(4, result.vocab().overallMetrics().lexicalDiversity().score());
    assertEquals("mid", result.vocab().overallMetrics().lexicalDiversity().level());
    assertEquals(10, result.vocab().overallMetrics().wordSophistication().score());

    // Verify coherence
    assertNotNull(result.coherence());
    assertNotNull(result.coherence().overallMetrics());
    assertEquals(6, result.coherence().overallMetrics().basicConnectives().score());
    assertEquals(List.of("and"), result.coherence().overallMetrics().basicConnectives().examples());

    // Verify fluency
    assertNotNull(result.fluency());
    assertEquals("0.7", result.fluency().fluencyVersion());
    assertEquals("0.4", result.fluency().ieltsSubscoreVersion());

    // Check segment metrics
    assertNotNull(result.fluency().segmentMetricsList());
    assertEquals(2, result.fluency().segmentMetricsList().size());

    SpeechEvaluation.SegmentMetrics firstSegment = result.fluency().segmentMetricsList().get(0);
    assertEquals(1.965F, firstSegment.duration());
    assertEquals(5.0890584F, firstSegment.speechRate());

    // Verify CEFR scores
    assertNotNull(result.cefrScore());
    assertEquals("C1+", result.cefrScore().pronunciation());
    assertEquals("C2", result.cefrScore().fluency());
    assertEquals("A2", result.cefrScore().grammar());
    assertEquals("A0", result.cefrScore().overall());
  }
}
