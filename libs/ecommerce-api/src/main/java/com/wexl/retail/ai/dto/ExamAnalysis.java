package com.wexl.retail.ai.dto;

import java.util.List;
import lombok.Builder;

public record ExamAnalysis() {
  @Builder
  public record PromptQuestionContent(Long questionNumber, int marks, String text, String answer) {}

  @Builder
  public record PromptAnswerContent(Long questionNumber, String answer) {}

  @Builder
  public record AiQuestionAnalysisResponse(Long question, int marks, String analysis) {}

  public record AiQuestionAnalysisResponseList(List<AiQuestionAnalysisResponse> response) {}

  public record MathAnalysisResponse(
      Long question, float marksAwarded, String reasonForMarksAwarded) {}

  public record MathAnalysisResponseList(List<MathAnalysisResponse> response) {}

  @Builder
  public record AnswerReAnalysis(String answer, float marks, String annotatedAnswer) {}

  @Builder
  public record TestEnrichmentAiResponse(
      String concept, String summary, TestEnrichData data, String references) {}

  public record TestEnrichData(List<Mcq> mcqs) {}

  @Builder
  public record AnswerEvaluationResponse(
      String question, String response, EvaluationResponse evaluation) {}

  public record EvaluationResponse(
      MarksDTO marks,
      RelevanceDetails relevance_to_question,
      RelevanceDetails main_idea_coverage,
      StrengthAreas strength_areas,
      CorrectionsAndImprovementAreas corrections_and_improvement_areas) {}

  public record MarksDTO(int relevance, int presentation) {}

  public record RelevanceDetails(int score_out_of_5, String comment, String example) {}

  public record StrengthAreas(Grammar grammar, Grammar vocabulary, Grammar oral_delivery) {}

  public record Grammar(String comment, String example) {}

  public record CorrectionsAndImprovementAreas(
      GrammarCorrection grammar, GrammarCorrection vocabulary, OralImprovement oral_improvement) {}

  public record GrammarCorrection(
      String issues, String Incorrect, String Correct, String improvementAreas) {}

  public record OralImprovement(List<String> suggestions, String improvement_areas) {}

  @Builder
  public record AiResponse(AiEvaluationResponse response) {}

  @Builder
  public record AiEvaluationResponse(
      Analysis relevanceToTheQuestionAsked,
      Analysis addressingMainIdea,
      GrammarAnalysis grammarVocabularyCorrections,
      String improvementAreas,
      String suggestedAnswerModelResponse) {}

  public record Analysis(String marks, String analysis) {}

  public record GrammarAnalysis(String original, String correctedVersion) {}

  @Builder
  public record Mcq(
      String question,
      String option1,
      String option2,
      String option3,
      String option4,
      Long answer,
      String explanation) {}
}
