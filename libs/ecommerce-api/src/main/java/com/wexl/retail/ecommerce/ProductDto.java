package com.wexl.retail.ecommerce;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

@Builder
public record ProductDto() {

  @Builder
  public record ProductResponseList(List<ProductResponse> productResponses) {}

  @Builder
  public record ProductResponse(
      String title,
      Long id,
      String status,
      String description,
      @JsonProperty("thumb_nail") String thumbNail,
      @JsonProperty("ext_ref") String extRef,
      CourseDefinitions courseDefinitions) {}

  @Builder
  public record Orders(
      Long id,
      @JsonProperty("customer") Customer Customer,
      @JsonProperty("line_items") List<LineItems> LineItems) {}

  @Builder
  public record Customer(
      Long id,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      String phone,
      String email) {}

  @Builder
  public record LineItems(Long id, String title, @JsonProperty("product_id") String productId) {}

  @Builder
  public record ProductOrdersResponse(
      String productId,
      Long orderId,
      String title,
      CourseDefinitions courseDefinitions,
      @JsonProperty("thumb_nail") String thumbnail,
      @JsonProperty("description") String description,
      @JsonProperty("product_km") Double productKm) {}

  @Builder
  public record CourseDefinitions(@JsonProperty("course_def_ids") List<Long> courseDefinitions) {}

  @Builder
  public record Courses(
      Long id,
      String name,
      String description,
      @JsonProperty("thumbnail") String thumbNail,
      @JsonProperty("course_km") Double courseKm) {}

  @Builder
  public record ProductCourses(
      List<Courses> courses,
      List<Bundles> bundles,
      @JsonProperty("product_km") Double productKm,
      String description,
      String title) {}

  @Builder
  public record Bundles(
      Long id,
      String name,
      @JsonProperty("thumbnail") String thumbNail,
      List<Courses> bundleCourses,
      @JsonProperty("bundle_km") Double bundleKm) {}

  @Builder
  public record Banners(List<Banner> banners) {}

  public record Banner(
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("image_url") String imageUrl,
      String description) {}
}
